/**
 * Strapi CMS API Integration
 *
 * This module provides functions to interact with the Strapi CMS API
 * for content management and data retrieval.
 */

import { getCachedPage, getCachedPages } from './strapi-cache';

const STRAPI_URL = process.env.NEXT_PUBLIC_STRAPI_API_URL || 'http://localhost:1339';
const API_TOKEN = process.env.STRAPI_API_TOKEN;

interface StrapiResponse<T> {
  data: T;
  meta?: {
    pagination?: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}

interface Banner {
  id: number;
  attributes: {
    title: string;
    description?: string;
    image?: {
      data?: {
        attributes: {
          url: string;
          alternativeText?: string;
        };
      };
    };
    link?: string;
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
  };
}

interface Product {
  id: number;
  documentId?: string;
  attributes?: {
    name: string;
    description?: any; // Rich text content
    short_description?: string;
    price: number;
    sale_price?: number;
    sku?: string;
    inventory_quantity?: number;
    product_status?: string;
    featured?: boolean;
    tags?: string;
    weight?: number;
    images?: {
      data?: Array<{
        attributes: {
          url: string;
          alternativeText?: string;
        };
      }>;
    };
    categories?: {
      data?: Array<{
        id: number;
        attributes: {
          name: string;
          slug: string;
        };
      }>;
    };
    isActive?: boolean;
    createdAt: string;
    updatedAt: string;
  };
  // Direct fields for existing Strapi structure
  name?: string;
  description?: any;
  short_description?: string;
  price?: number;
  sale_price?: number;
  sku?: string;
  inventory_quantity?: number;
  product_status?: string;
  featured?: boolean;
  tags?: string;
  weight?: number;
  createdAt?: string;
  updatedAt?: string;
}

interface Category {
  id: number;
  documentId?: string;
  attributes?: {
    name: string;
    slug: string;
    description?: any; // Rich text content
    featured?: boolean;
    isSubcategory?: boolean; // New field for hierarchy
    image?: {
      data?: {
        attributes: {
          url: string;
          alternativeText?: string;
        };
      };
    };
    parent?: {
      data?: {
        id: number;
        attributes: {
          name: string;
          slug: string;
        };
      };
    };
    children?: {
      data?: Array<{
        id: number;
        attributes: {
          name: string;
          slug: string;
        };
      }>;
    };
    products?: {
      data?: Array<{
        id: number;
        attributes: {
          name: string;
          price: number;
        };
      }>;
    };
    createdAt: string;
    updatedAt: string;
  };
  // Direct fields for existing Strapi structure
  name?: string;
  slug?: string;
  description?: any;
  featured?: boolean;
  isSubcategory?: boolean; // New field for hierarchy
  parent?: any;
  children?: any[];
  createdAt?: string;
  updatedAt?: string;
}

export interface Page {
  id: number;
  documentId?: string;
  title: string;
  slug: string;
  content: string;
  excerpt?: string;
  metaTitle?: string;
  metaDescription?: string;
  status?: 'draft' | 'published' | 'archived';
  template?: 'default' | 'landing' | 'contact' | 'about';
  featured?: boolean;
  publishedAt?: string;
  viewCount?: number;
  author?: string;
  featured_image?: {
    data?: {
      attributes: {
        url: string;
        alternativeText?: string;
      };
    };
  };
  seo?: {
    meta_title?: string;
    meta_description?: string;
    keywords?: string;
    canonical_url?: string;
  };
  createdAt: string;
  updatedAt: string;
}

/**
 * Generic API request function
 */
async function strapiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<StrapiResponse<T>> {
  const url = `${STRAPI_URL}/api${endpoint}`;

  const defaultHeaders: Record<string, string> = {
    'Content-Type': 'application/json',
  };

  if (API_TOKEN) {
    defaultHeaders['Authorization'] = `Bearer ${API_TOKEN}`;
  }

  const config: RequestInit = {
    headers: {
      ...defaultHeaders,
      ...options.headers,
    },
    ...options,
  };

  try {
    const response = await fetch(url, config);

    if (!response.ok) {
      throw new Error(`Strapi API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Strapi API request failed:', error);
    throw error;
  }
}

/**
 * Get all banners
 */
export async function getBanners(): Promise<StrapiResponse<Banner[]>> {
  try {
    return await strapiRequest<Banner[]>('/banners?populate=*');
  } catch (error) {
    console.error('Failed to fetch banners:', error);
    // Return mock data as fallback
    return {
      data: [
        {
          id: 1,
          attributes: {
            title: 'Welcome to ONDC Seller Platform',
            description: "Start selling on India's Open Network for Digital Commerce",
            link: '/dashboard',
            isActive: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
        },
        {
          id: 2,
          attributes: {
            title: 'Boost Your Sales',
            description: 'Reach millions of customers across India',
            link: '/products',
            isActive: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
        },
      ],
    };
  }
}

/**
 * Get banner by ID
 */
export async function getBannerById(id: number): Promise<StrapiResponse<Banner>> {
  return await strapiRequest<Banner>(`/banners/${id}?populate=*`);
}

/**
 * Get all categories
 */
export async function getCategories(params?: {
  page?: number;
  pageSize?: number;
  featured?: boolean;
  parentOnly?: boolean; // New parameter for hierarchy
  populate?: string;
}): Promise<StrapiResponse<Category[]>> {
  // Build query string manually to avoid URL encoding issues
  const queryParts: string[] = [];

  if (params?.page) {
    queryParts.push(`pagination[page]=${params.page}`);
  }

  if (params?.pageSize) {
    queryParts.push(`pagination[pageSize]=${params.pageSize}`);
  }

  // Note: Strapi filter syntax is causing 400 errors, so we'll fetch all and filter client-side
  // Only add populate if it's explicitly provided and not the default
  if (params?.populate && params.populate !== 'image,parent,children') {
    queryParts.push(`populate=${params.populate}`);
  }

  const queryString = queryParts.join('&');
  const endpoint = `/product-categories?${queryString}`;

  try {
    console.log('🚀 Fetching categories from Strapi:', endpoint);
    const response = await strapiRequest<Category[]>(endpoint);

    // Transform Strapi v5 data format if needed
    if (response.data && Array.isArray(response.data)) {
      let transformedData = response.data.map(transformStrapiCategory);

      // Filter by featured status if requested (backward compatibility)
      if (params?.featured !== undefined) {
        transformedData = transformedData.filter(category => category.featured === params.featured);
        console.log(`🔍 Filtered to ${transformedData.length} featured categories`);
      }

      // Filter by parent-only categories (new hierarchy logic)
      if (params?.parentOnly !== undefined) {
        transformedData = transformedData.filter(category => {
          // If isSubcategory field exists, use it; otherwise fall back to parent field
          if (category.isSubcategory !== undefined) {
            return params.parentOnly ? !category.isSubcategory : category.isSubcategory;
          } else {
            // Fallback: parent categories have no parent
            return params.parentOnly ? !category.parent : !!category.parent;
          }
        });
        console.log(
          `🔍 Filtered to ${transformedData.length} ${params.parentOnly ? 'parent' : 'sub'} categories`
        );
      }

      console.log(`✅ Successfully fetched ${transformedData.length} categories from Strapi`);
      return {
        ...response,
        data: transformedData,
      };
    }

    return response;
  } catch (error) {
    console.error('❌ Failed to fetch categories from Strapi:', error);
    console.log('🔄 Using fallback categories data');
    // Return comprehensive fallback data with string descriptions
    return {
      data: [
        {
          id: 1,
          name: 'Electronics',
          slug: 'electronics',
          description: 'Latest gadgets and tech devices',
          featured: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: 2,
          name: 'Fashion',
          slug: 'fashion',
          description: 'Trendy clothing and accessories',
          featured: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: 3,
          name: 'Home & Garden',
          slug: 'home-garden',
          description: 'Furniture and home decor',
          featured: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: 4,
          name: 'Sports & Fitness',
          slug: 'sports-fitness',
          description: 'Athletic gear and equipment',
          featured: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ],
      meta: {
        pagination: {
          page: 1,
          pageSize: 25,
          pageCount: 1,
          total: 4,
        },
      },
    };
  }
}

/**
 * Get subcategories by parent category ID
 */
export async function getSubcategoriesByParent(
  parentId: number
): Promise<StrapiResponse<Category[]>> {
  try {
    console.log('🚀 Fetching subcategories for parent:', parentId);

    // Try using isSubcategory field first, then fall back to parent relation
    let endpoint = `/product-categories?filters[parent][id][$eq]=${parentId}&populate=*`;

    const response = await strapiRequest<Category[]>(endpoint);

    if (response.data && Array.isArray(response.data)) {
      const transformedData = response.data.map(transformStrapiCategory);
      console.log(
        `✅ Successfully fetched ${transformedData.length} subcategories for parent ${parentId}`
      );
      return {
        ...response,
        data: transformedData,
      };
    }

    return response;
  } catch (error) {
    console.error(`❌ Failed to fetch subcategories for parent ${parentId}:`, error);
    return {
      data: [],
      meta: {
        pagination: {
          page: 1,
          pageSize: 25,
          pageCount: 1,
          total: 0,
        },
      },
    };
  }
}

/**
 * Get category by slug
 */
export async function getCategoryBySlug(slug: string): Promise<Category | null> {
  try {
    console.log('🚀 Fetching category by slug:', slug);
    const response = await strapiRequest<Category[]>(
      `/product-categories?filters[slug][$eq]=${slug}&populate=*`
    );

    if (response.data && response.data.length > 0) {
      return response.data[0];
    }

    return null;
  } catch (error) {
    console.error(`❌ Failed to fetch category with slug ${slug}:`, error);

    // Return fallback category based on slug with string descriptions
    const fallbackCategories: Record<string, Category> = {
      electronics: {
        id: 1,
        name: 'Electronics',
        slug: 'electronics',
        description: 'Latest gadgets and tech devices',
        featured: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      fashion: {
        id: 2,
        name: 'Fashion',
        slug: 'fashion',
        description: 'Trendy clothing and accessories',
        featured: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    };

    return fallbackCategories[slug] || null;
  }
}

/**
 * Get all products
 */
export async function getProducts(params?: {
  page?: number;
  pageSize?: number;
  category?: string;
  featured?: boolean;
  populate?: string;
}): Promise<StrapiResponse<Product[]>> {
  // Build query string manually to avoid URL encoding issues
  const queryParts: string[] = [];

  if (params?.page) {
    queryParts.push(`pagination[page]=${params.page}`);
  }

  if (params?.pageSize) {
    queryParts.push(`pagination[pageSize]=${params.pageSize}`);
  }

  if (params?.category) {
    // Check if category is a number (ID) or string (slug)
    const isNumeric = /^\d+$/.test(params.category);
    if (isNumeric) {
      queryParts.push(`filters[categories][id][$eq]=${params.category}`);
    } else {
      queryParts.push(`filters[categories][slug][$eq]=${params.category}`);
    }
  }

  // Always populate categories and images to ensure we have the data we need
  queryParts.push('populate=*');

  const queryString = queryParts.join('&');
  const endpoint = `/products?${queryString}`;

  try {
    console.log('🚀 Fetching products from Strapi:', endpoint);
    const response = await strapiRequest<Product[]>(endpoint);

    // Transform Strapi v4 data format if needed
    if (response.data && Array.isArray(response.data)) {
      let transformedData = response.data.map(transformStrapiProduct);

      // Filter by featured status if requested
      if (params?.featured !== undefined) {
        transformedData = transformedData.filter(product => product.featured === params.featured);
        console.log(`🔍 Filtered to ${transformedData.length} featured products`);
      }

      console.log(`✅ Successfully fetched ${transformedData.length} products from Strapi`);
      return {
        ...response,
        data: transformedData,
      };
    }

    return response;
  } catch (error) {
    console.error('❌ Failed to fetch products from Strapi:', error);
    console.log('🔄 Using fallback products data');
    // Return mock data as fallback with string descriptions
    return {
      data: [
        {
          id: 1,
          name: 'Organic Apples',
          description: 'Fresh organic apples from the farm',
          short_description: 'Organic apples',
          price: 150,
          sale_price: 120,
          sku: 'ORG-APL-001',
          inventory_quantity: 100,
          product_status: 'Published',
          featured: true,
          tags: 'Organic, Fruit, Healthy',
          weight: 1,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: 2,
          name: 'Smartphone X',
          description: 'Latest smartphone with advanced features',
          short_description: 'Premium smartphone',
          price: 50000,
          sale_price: 45000,
          sku: 'TECH-SPX-001',
          inventory_quantity: 50,
          product_status: 'Published',
          featured: true,
          tags: 'Smartphone, Tech, Premium',
          weight: 0.2,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ],
      meta: {
        pagination: {
          page: 1,
          pageSize: 25,
          pageCount: 1,
          total: 2,
        },
      },
    };
  }
}

/**
 * Get product by slug
 */
export async function getProductBySlug(slug: string): Promise<Product | null> {
  try {
    console.log('🔍 Fetching product by slug:', slug);

    const response = await strapiAPI.get(`/products`, {
      params: {
        'filters[slug][$eq]': slug,
        populate: '*',
        'pagination[pageSize]': 1,
      },
    });

    const products = response.data.data || [];

    if (products.length === 0) {
      console.log('⚠️ No product found with slug:', slug);
      return null;
    }

    const strapiProduct = products[0];
    console.log('✅ Found product:', strapiProduct.name);

    return transformStrapiProduct(strapiProduct);
  } catch (error) {
    console.error('❌ Error fetching product by slug:', error);
    return null;
  }
}

/**
 * Get products by category
 */
export async function getProductsByCategory(
  categorySlugOrId: string | number,
  params?: {
    page?: number;
    pageSize?: number;
    populate?: string;
    includeSubcategories?: boolean;
  }
): Promise<StrapiResponse<Product[]>> {
  try {
    const { includeSubcategories = false, ...otherParams } = params || {};

    if (includeSubcategories && typeof categorySlugOrId === 'number') {
      // Fetch products from parent category and all its subcategories
      console.log('🔍 Fetching products from parent category and subcategories:', categorySlugOrId);

      // First get all subcategories of this parent
      const subcategoriesResponse = await getSubcategoriesByParent(categorySlugOrId);
      const subcategories = subcategoriesResponse.data || [];

      console.log('📊 Found subcategories:', subcategories.length);

      if (subcategories.length === 0) {
        // No subcategories, fetch products directly from parent category
        return getProducts({
          ...otherParams,
          category: categorySlugOrId.toString(),
        });
      }

      // Get all subcategory IDs
      const subcategoryIds = subcategories.map(sub => sub.id);

      // Fetch products from all subcategories
      const allProducts: Product[] = [];
      const seenProductIds = new Set<string | number>();

      for (const subcategoryId of subcategoryIds) {
        try {
          const productsResponse = await getProducts({
            ...otherParams,
            category: subcategoryId.toString(),
            pageSize: 100, // Get more products per subcategory
          });

          if (productsResponse.data) {
            // Filter out duplicate products
            const uniqueProducts = productsResponse.data.filter(product => {
              if (seenProductIds.has(product.id)) {
                return false;
              }
              seenProductIds.add(product.id);
              return true;
            });
            allProducts.push(...uniqueProducts);
          }
        } catch (error) {
          console.warn(`⚠️ Failed to fetch products from subcategory ${subcategoryId}:`, error);
        }
      }

      console.log('📊 Total products from all subcategories:', allProducts.length);

      // Apply pagination to combined results
      const pageSize = otherParams.pageSize || 20;
      const page = otherParams.page || 1;
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedProducts = allProducts.slice(startIndex, endIndex);

      return {
        data: paginatedProducts,
        meta: {
          pagination: {
            page,
            pageSize,
            pageCount: Math.ceil(allProducts.length / pageSize),
            total: allProducts.length,
          },
        },
      };
    } else {
      // Regular category fetch
      return getProducts({
        ...otherParams,
        category: categorySlugOrId.toString(),
      });
    }
  } catch (error) {
    console.error('❌ Error in getProductsByCategory:', error);
    throw error;
  }
}

/**
 * Get product by ID
 */
export async function getProductById(id: number): Promise<StrapiResponse<Product>> {
  return await strapiRequest<Product>(`/products/${id}?populate=*`);
}

/**
 * Create a new banner
 */
export async function createBanner(
  data: Partial<Banner['attributes']>
): Promise<StrapiResponse<Banner>> {
  return await strapiRequest<Banner>('/banners', {
    method: 'POST',
    body: JSON.stringify({ data }),
  });
}

/**
 * Update a banner
 */
export async function updateBanner(
  id: number,
  data: Partial<Banner['attributes']>
): Promise<StrapiResponse<Banner>> {
  return await strapiRequest<Banner>(`/banners/${id}`, {
    method: 'PUT',
    body: JSON.stringify({ data }),
  });
}

/**
 * Delete a banner
 */
export async function deleteBanner(id: number): Promise<StrapiResponse<Banner>> {
  return await strapiRequest<Banner>(`/banners/${id}`, {
    method: 'DELETE',
  });
}

/**
 * Get all pages
 */
export async function getPages(params?: {
  page?: number;
  pageSize?: number;
  status?: string;
  featured?: boolean;
}): Promise<StrapiResponse<Page[]>> {
  const searchParams = new URLSearchParams();

  if (params?.page) {
    searchParams.append('pagination[page]', params.page.toString());
  }

  if (params?.pageSize) {
    searchParams.append('pagination[pageSize]', params.pageSize.toString());
  }

  if (params?.status) {
    searchParams.append('filters[status][$eq]', params.status);
  }

  if (params?.featured !== undefined) {
    searchParams.append('filters[featured][$eq]', params.featured.toString());
  }

  const queryString = searchParams.toString();
  const endpoint = `/pages?populate=*${queryString ? `&${queryString}` : ''}`;

  try {
    return await strapiRequest<Page[]>(endpoint);
  } catch (error) {
    console.error('Failed to fetch pages:', error);
    // Return mock data as fallback
    return {
      data: [
        {
          id: 1,
          title: 'About Us',
          slug: 'about-us',
          content:
            '<h2>Welcome to ONDC Seller Platform</h2><p>We are a marketplace connecting buyers and sellers across India through the Open Network for Digital Commerce (ONDC).</p>',
          excerpt: 'Learn about ONDC Seller Platform',
          metaTitle: 'About Us - ONDC Seller Platform',
          metaDescription:
            'Discover how ONDC Seller Platform is democratizing digital commerce in India.',
          status: 'published',
          template: 'about',
          featured: false,
          author: 'Admin',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ],
      meta: {
        pagination: {
          page: 1,
          pageSize: 25,
          pageCount: 1,
          total: 1,
        },
      },
    };
  }
}

/**
 * Get page by slug
 */
export async function getPageBySlug(slug: string): Promise<Page | null> {
  return getCachedPage(slug, async () => {
    try {
      const response = await strapiRequest<Page[]>(`/pages?filters[slug][$eq]=${slug}&populate=*`);

      if (response.data && response.data.length > 0) {
        return response.data[0];
      }

      return null;
    } catch (error) {
      console.error(`Failed to fetch page with slug ${slug}:`, error);

      // Return fallback content based on slug
      const fallbackPages: Record<string, Page> = {
        'about-us': {
          id: 1,
          title: 'About Us',
          slug: 'about-us',
          content:
            '<h2>Welcome to ONDC Seller Platform</h2><p>We are a marketplace connecting buyers and sellers across India through the Open Network for Digital Commerce (ONDC).</p><h2>Our Mission</h2><p>Our mission is to democratize digital commerce in India by providing a platform that enables sellers of all sizes to reach customers across the country.</p>',
          excerpt: 'Learn about ONDC Seller Platform',
          metaTitle: 'About Us - ONDC Seller Platform',
          metaDescription:
            'Discover how ONDC Seller Platform is democratizing digital commerce in India.',
          status: 'published',
          template: 'about',
          featured: false,
          author: 'Admin',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        contact: {
          id: 2,
          title: 'Contact Us',
          slug: 'contact',
          content:
            "<h2>Get in Touch</h2><p>Have questions about the ONDC Seller Platform? We're here to help!</p>",
          excerpt: 'Contact ONDC Seller Platform for support',
          metaTitle: 'Contact Us - ONDC Seller Platform',
          metaDescription: 'Get in touch with ONDC Seller Platform support team.',
          status: 'published',
          template: 'contact',
          featured: false,
          author: 'Admin',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      };

      return fallbackPages[slug] || null;
    }
  });
}

/**
 * Get page by ID
 */
export async function getPageById(id: number): Promise<StrapiResponse<Page>> {
  return await strapiRequest<Page>(`/pages/${id}?populate=*`);
}

/**
 * Create a new page
 */
export async function createPage(data: Partial<Page>): Promise<StrapiResponse<Page>> {
  return await strapiRequest<Page>('/pages', {
    method: 'POST',
    body: JSON.stringify({ data }),
  });
}

/**
 * Update a page
 */
export async function updatePage(id: number, data: Partial<Page>): Promise<StrapiResponse<Page>> {
  return await strapiRequest<Page>(`/pages/${id}`, {
    method: 'PUT',
    body: JSON.stringify({ data }),
  });
}

/**
 * Delete a page
 */
export async function deletePage(id: number): Promise<StrapiResponse<Page>> {
  return await strapiRequest<Page>(`/pages/${id}`, {
    method: 'DELETE',
  });
}

/**
 * GraphQL query function
 */
export async function strapiGraphQLQuery<T>(
  query: string,
  variables?: Record<string, any>
): Promise<T> {
  const url = `${STRAPI_URL}/graphql`;

  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  };

  if (API_TOKEN) {
    headers['Authorization'] = `Bearer ${API_TOKEN}`;
  }

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        query,
        variables,
      }),
    });

    if (!response.ok) {
      throw new Error(`GraphQL request failed: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();

    if (result.errors) {
      throw new Error(`GraphQL errors: ${JSON.stringify(result.errors)}`);
    }

    return result.data;
  } catch (error) {
    console.error('GraphQL query failed:', error);
    throw error;
  }
}

// Additional functions for the demo page
export async function getCachedBanners() {
  return getBanners();
}

export async function getPaginatedProducts(params: { page: number; pageSize: number }) {
  return getProducts(params);
}

/**
 * Transform Strapi v5 category data to expected format
 */
function transformStrapiCategory(strapiCategory: any): Category {
  // Handle both Strapi v5 format (direct fields) and v4 format (with attributes)
  const attributes = strapiCategory.attributes || strapiCategory;

  return {
    id: strapiCategory.id,
    name: attributes.name || strapiCategory.name || 'Unknown Category',
    slug: attributes.slug || strapiCategory.slug || '',
    description:
      extractTextFromRichText(attributes.description || strapiCategory.description) ||
      attributes.short_description ||
      'No description available',
    featured: attributes.featured ?? strapiCategory.featured ?? false,
    isSubcategory: attributes.isSubcategory ?? strapiCategory.isSubcategory ?? false, // New field
    parent: attributes.parent || strapiCategory.parent || null, // Hierarchy support
    children: attributes.children || strapiCategory.children || [], // Hierarchy support
    createdAt: attributes.createdAt || strapiCategory.createdAt || new Date().toISOString(),
    updatedAt: attributes.updatedAt || strapiCategory.updatedAt || new Date().toISOString(),
  };
}

/**
 * Transform Strapi v4 product data to expected format
 */
function transformStrapiProduct(strapiProduct: any): Product {
  // Handle both Strapi v5 format (direct fields) and v4 format (with attributes)
  // In v5, fields are directly on the object; in v4, they're in attributes
  const name = strapiProduct.name || strapiProduct.attributes?.name || 'Unknown Product';
  const description = strapiProduct.description || strapiProduct.attributes?.description;
  const short_description =
    strapiProduct.short_description || strapiProduct.attributes?.short_description;
  const price = strapiProduct.price || strapiProduct.attributes?.price || 0;
  const sale_price = strapiProduct.sale_price || strapiProduct.attributes?.sale_price || null;
  const sku = strapiProduct.sku || strapiProduct.attributes?.sku || `PROD-${strapiProduct.id}`;
  const inventory_quantity =
    strapiProduct.inventory_quantity || strapiProduct.attributes?.inventory_quantity || 0;
  const product_status =
    strapiProduct.product_status || strapiProduct.attributes?.product_status || 'Published';
  const featured = strapiProduct.featured || strapiProduct.attributes?.featured || false;
  const tags = strapiProduct.tags || strapiProduct.attributes?.tags || '';
  const weight = strapiProduct.weight || strapiProduct.attributes?.weight || 0;
  const createdAt =
    strapiProduct.createdAt || strapiProduct.attributes?.createdAt || new Date().toISOString();
  const updatedAt =
    strapiProduct.updatedAt || strapiProduct.attributes?.updatedAt || new Date().toISOString();

  return {
    id: strapiProduct.id,
    name,
    description:
      extractTextFromRichText(description) || short_description || 'No description available',
    short_description: short_description || name || 'No description',
    price,
    sale_price,
    sku,
    inventory_quantity,
    product_status,
    featured,
    tags,
    weight,
    createdAt,
    updatedAt,
  };
}

/**
 * Extract text content from rich text format
 */
function extractTextFromRichText(richText: any): string {
  if (!richText) return '';

  if (typeof richText === 'string') {
    return richText;
  }

  if (Array.isArray(richText)) {
    return richText
      .map((block: any) => {
        if (block.children && Array.isArray(block.children)) {
          return block.children.map((child: any) => child.text || '').join('');
        }
        return '';
      })
      .join(' ');
  }

  return '';
}

/**
 * Get footer content from Strapi CMS
 */
export async function getFooters(): Promise<StrapiResponse<any[]>> {
  try {
    return await strapiRequest<any[]>('/footers?populate=*');
  } catch (error) {
    console.error('Failed to fetch footers:', error);
    // Return empty data as fallback
    return {
      data: [],
    };
  }
}

export async function getProductsWithRelations() {
  const query = `
    query GetProductsWithRelations {
      products {
        data {
          id
          attributes {
            name
            description
            price
            sku
            category
            isActive
            createdAt
            updatedAt
          }
        }
      }
    }
  `;

  try {
    return await strapiGraphQLQuery(query);
  } catch (error) {
    console.warn('GraphQL query failed, returning mock data:', error);
    return {
      products: {
        data: [
          {
            id: '1',
            attributes: {
              name: 'Mock Product 1',
              description: 'This is a mock product from GraphQL fallback',
              price: 999,
              sku: 'MOCK-001',
              category: 'Electronics',
              isActive: true,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            },
          },
        ],
      },
    };
  }
}

export async function fetchGraphQL(query: string, variables?: Record<string, any>) {
  return strapiGraphQLQuery(query, variables);
}

export function clearCache() {
  console.log('Cache cleared');
}
