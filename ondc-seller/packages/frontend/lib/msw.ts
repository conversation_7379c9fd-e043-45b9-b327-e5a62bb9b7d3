/**
 * Mock Service Worker (MSW) Setup
 *
 * This module sets up MSW for API mocking during development and testing.
 */

// MSW imports - only for browser environment
let setupWorker: any = null;
let http: any = null;

// Dynamically import MSW only in browser environment
async function initializeMSW() {
  if (typeof window !== 'undefined') {
    try {
      const [mswBrowser, mswCore] = await Promise.all([import('msw/browser'), import('msw')]);
      setupWorker = mswBrowser.setupWorker;
      http = mswCore.http;
      return true;
    } catch (error) {
      console.warn('MSW not available:', error);
      return false;
    }
  }
  return false;
}

// Import comprehensive mock data
import {
  mockProducts,
  mockBanners,
  getProductsByCategory,
  getFilteredProducts,
} from '../data/msw-data';

// API handlers - create function to get handlers
export const getHandlers = async () => {
  const initialized = await initializeMSW();
  if (!initialized || !http) return [];

  return [
    // Products API
    http.get('/api/products', (req: any, res: any, ctx: any) => {
      const page = parseInt(req.url.searchParams.get('page') || '1');
      const limit = parseInt(req.url.searchParams.get('limit') || '10');
      const category = req.url.searchParams.get('category');
      const search = req.url.searchParams.get('search');

      let filteredProducts = [...mockProducts];

      // Filter by category using the new category field
      if (category) {
        filteredProducts = getProductsByCategory(category);
      }

      // Filter by search
      if (search) {
        filteredProducts = getFilteredProducts(filteredProducts, { search });
      }

      // Pagination
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedProducts = filteredProducts.slice(startIndex, endIndex);

      return res(
        ctx.status(200),
        ctx.json({
          products: paginatedProducts,
          pagination: {
            page,
            limit,
            total: filteredProducts.length,
            totalPages: Math.ceil(filteredProducts.length / limit),
          },
        })
      );
    }),

    // Category-specific products API
    http.get('/api/categories/:slug/products', (req: any, res: any, ctx: any) => {
      const { slug } = req.params;
      const page = parseInt(req.url.searchParams.get('page') || '1');
      const limit = parseInt(req.url.searchParams.get('limit') || '12');
      const sort = req.url.searchParams.get('sort') || 'featured';
      const priceRange = req.url.searchParams.get('priceRange');

      // Map slug to category
      const categoryMap: { [key: string]: string } = {
        electronics: 'electronics',
        fashion: 'fashion',
        'home-garden': 'home-garden',
        'sports-fitness': 'sports-fitness',
        'books-media': 'books-media',
        'beauty-health': 'beauty-health',
      };

      const category = categoryMap[slug];
      if (!category) {
        return res(ctx.status(404), ctx.json({ error: 'Category not found' }));
      }

      let filteredProducts = getProductsByCategory(category);

      // Apply price range filter
      if (priceRange) {
        const [min, max] = priceRange.split('-').map(Number);
        filteredProducts = filteredProducts.filter(
          product => product.price >= min * 100 && product.price <= max * 100
        );
      }

      // Apply sorting
      switch (sort) {
        case 'price-low':
          filteredProducts.sort((a, b) => a.price - b.price);
          break;
        case 'price-high':
          filteredProducts.sort((a, b) => b.price - a.price);
          break;
        case 'rating':
          filteredProducts.sort((a, b) => (b.rating || 0) - (a.rating || 0));
          break;
        case 'newest':
          filteredProducts.sort(
            (a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
          );
          break;
        default: // featured
          filteredProducts.sort((a, b) => (b.reviews || 0) - (a.reviews || 0));
      }

      // Pagination
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedProducts = filteredProducts.slice(startIndex, endIndex);

      return res(
        ctx.status(200),
        ctx.json({
          products: paginatedProducts,
          pagination: {
            page,
            limit,
            total: filteredProducts.length,
            totalPages: Math.ceil(filteredProducts.length / limit),
          },
          category: {
            slug,
            name: category.charAt(0).toUpperCase() + category.slice(1).replace('-', ' & '),
            productCount: filteredProducts.length,
          },
        })
      );
    }),

    http.get('/api/products/:id', (req: any, res: any, ctx: any) => {
      const { id } = req.params;
      const product = mockProducts.find(p => p.id === id);

      if (!product) {
        return res(ctx.status(404), ctx.json({ error: 'Product not found' }));
      }

      return res(ctx.status(200), ctx.json({ product }));
    }),

    // Banners API
    http.get('/api/banners', (req: any, res: any, ctx: any) => {
      return res(
        ctx.status(200),
        ctx.json({
          data: mockBanners,
          meta: {
            pagination: {
              page: 1,
              pageSize: 25,
              pageCount: 1,
              total: mockBanners.length,
            },
          },
        })
      );
    }),

    // Cart API
    http.post('/api/test-cart', (req: any, res: any, ctx: any) => {
      return res(
        ctx.status(200),
        ctx.json({
          cart: {
            id: 'cart_123',
            items: [],
            subtotal: 0,
            total: 0,
            item_count: 0,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          },
        })
      );
    }),

    // Error simulation
    http.get('/api/error-test', (req: any, res: any, ctx: any) => {
      const errorType = req.url.searchParams.get('type');

      switch (errorType) {
        case 'network':
          return res.networkError('Network error');
        case '404':
          return res(ctx.status(404), ctx.json({ error: 'Not found' }));
        case '500':
          return res(ctx.status(500), ctx.json({ error: 'Internal server error' }));
        default:
          return res(ctx.status(200), ctx.json({ message: 'No error' }));
      }
    }),

    // Strapi API mocks
    http.get(
      `${process.env.NEXT_PUBLIC_STRAPI_API_URL}/api/banners`,
      (req: any, res: any, ctx: any) => {
        return res(
          ctx.status(200),
          ctx.json({
            data: mockBanners,
            meta: {
              pagination: {
                page: 1,
                pageSize: 25,
                pageCount: 1,
                total: mockBanners.length,
              },
            },
          })
        );
      }
    ),

    http.get(
      `${process.env.NEXT_PUBLIC_STRAPI_API_URL}/api/products`,
      (req: any, res: any, ctx: any) => {
        return res(
          ctx.status(200),
          ctx.json({
            data: mockProducts.map(product => ({
              id: product.id,
              attributes: {
                name: product.title,
                description: product.description,
                price: product.price,
                sku: `SKU-${product.id}`,
                category: product.product_type,
                isActive: true,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
              },
            })),
            meta: {
              pagination: {
                page: 1,
                pageSize: 25,
                pageCount: 1,
                total: mockProducts.length,
              },
            },
          })
        );
      }
    ),
  ];
};

// Setup for browser
export const createWorker = async () => {
  const initialized = await initializeMSW();
  if (!initialized || !setupWorker) return null;

  const handlers = await getHandlers();
  if (handlers.length === 0) return null;

  return setupWorker(...handlers);
};

// Start MSW
export async function startMSW() {
  if (typeof window !== 'undefined') {
    try {
      const worker = await createWorker();
      if (worker) {
        await worker.start({
          onUnhandledRequest: 'bypass',
        });
        console.log('🔶 MSW started in browser');
        return worker;
      }
    } catch (error) {
      console.warn('Failed to start MSW:', error);
    }
  }
  return null;
}

// Stop MSW
export async function stopMSW() {
  if (typeof window !== 'undefined') {
    try {
      const worker = await createWorker();
      if (worker) {
        worker.stop();
        console.log('🔶 MSW stopped');
      }
    } catch (error) {
      console.warn('Failed to stop MSW:', error);
    }
  }
}

// Enable/disable MSW based on environment
export const isMSWEnabled = () => {
  if (typeof process === 'undefined') return false;
  return process.env.NODE_ENV === 'development' || process.env.NEXT_PUBLIC_MSW_ENABLED === 'true';
};

// Export worker for compatibility
export const worker = {
  start: startMSW,
  stop: stopMSW,
};

// Additional utility functions for compatibility
export function isMSWEnabledInStorage(): boolean {
  if (typeof window === 'undefined') return false;

  try {
    return localStorage.getItem('msw-enabled') === 'true';
  } catch {
    return false;
  }
}
