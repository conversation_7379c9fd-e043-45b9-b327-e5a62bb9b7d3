'use client';

import React, { useState, useEffect } from 'react';
import Image from '@/components/ui/Image';
import Link from 'next/link';

interface Category {
  id: string;
  name: string;
  description: string;
  image: string;
  itemCount: number;
  color: string;
  subcategories: {
    name: string;
    itemCount: number;
  }[];
}

interface StrapiCategory {
  id: string;
  name: string;
  description?: string;
  image?: string;
  featured?: boolean;
  handle?: string;
}

// Loading skeleton component
const CategorySkeleton = () => (
  <div className="group relative overflow-hidden rounded-2xl bg-gradient-to-br shadow-sm h-80 flex flex-col animate-pulse">
    <div className="relative overflow-hidden rounded-2xl bg-gray-200 flex-1 flex flex-col">
      <div className="relative w-full h-40 flex-shrink-0 bg-gray-300"></div>
      <div className="absolute bottom-0 left-0 right-0 p-6 flex-1 flex flex-col justify-end">
        <div className="flex items-center justify-between">
          <div className="flex-1 min-w-0">
            <div className="h-6 bg-gray-300 rounded mb-2 w-3/4"></div>
            <div className="h-4 bg-gray-300 rounded w-full"></div>
          </div>
          <div className="bg-gray-300 rounded-full px-3 py-1 w-20 h-8"></div>
        </div>
        <div className="mt-4">
          <div className="flex flex-wrap gap-2">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="h-6 bg-gray-300 rounded-full w-16"></div>
            ))}
          </div>
        </div>
        <div className="mt-4 h-4 bg-gray-300 rounded w-32"></div>
      </div>
    </div>
  </div>
);

const CategoriesPage = () => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fallback categories with proper image paths and enhanced data
  const defaultCategories: Category[] = [
    {
      id: 'electronics',
      name: 'Electronics',
      description: 'Latest gadgets and tech',
      image: '/images/categories/electronics.jpg',
      itemCount: 1250,
      color: 'blue',
      subcategories: [
        { name: 'Headphones', itemCount: 45 },
        { name: 'Smartwatches', itemCount: 32 },
        { name: 'Cameras', itemCount: 28 },
        { name: 'Keyboards', itemCount: 67 },
      ],
    },
    {
      id: 'fashion',
      name: 'Fashion',
      description: 'Trendy clothing & accessories',
      image: '/images/categories/fashion.jpg',
      itemCount: 2100,
      color: 'pink',
      subcategories: [
        { name: 'T-Shirts', itemCount: 156 },
        { name: 'Jeans', itemCount: 89 },
        { name: 'Dresses', itemCount: 134 },
        { name: 'Bags', itemCount: 78 },
      ],
    },
    {
      id: 'home-garden',
      name: 'Home & Garden',
      description: 'Furniture & home decor',
      image: '/images/categories/home-garden.jpg',
      itemCount: 890,
      color: 'amber',
      subcategories: [
        { name: 'Coffee Makers', itemCount: 23 },
        { name: 'Office Chairs', itemCount: 34 },
        { name: 'Desk Lamps', itemCount: 45 },
        { name: 'Cutting Boards', itemCount: 67 },
      ],
    },
    {
      id: 'sports-fitness',
      name: 'Sports & Fitness',
      description: 'Athletic gear & equipment',
      image: '/images/categories/sports-fitness.jpg',
      itemCount: 650,
      color: 'green',
      subcategories: [
        { name: 'Yoga Mats', itemCount: 34 },
        { name: 'Dumbbells', itemCount: 28 },
        { name: 'Resistance Bands', itemCount: 45 },
        { name: 'Sports Headphones', itemCount: 23 },
      ],
    },
  ];

  // Transform Strapi category to local category format
  const transformStrapiCategory = (strapiCategory: StrapiCategory): Category => {
    // Map category names to colors and generate subcategories
    const categoryColorMap: Record<string, string> = {
      electronics: 'blue',
      fashion: 'pink',
      home: 'amber',
      garden: 'green',
      sports: 'green',
      fitness: 'green',
      books: 'purple',
      media: 'purple',
      beauty: 'red',
      health: 'red',
      automotive: 'gray',
      toys: 'orange',
      games: 'orange',
      organic: 'green',
      food: 'amber',
      smartphones: 'blue',
      majestic: 'purple',
      mountain: 'green',
      graphic: 'blue',
    };

    const name = strapiCategory.name.toLowerCase();
    let color = 'blue'; // default

    // Find matching color based on category name
    for (const [key, value] of Object.entries(categoryColorMap)) {
      if (name.includes(key)) {
        color = value;
        break;
      }
    }

    // Generate mock subcategories based on category type
    const generateSubcategories = (categoryName: string) => {
      const subcategoryMap: Record<string, { name: string; itemCount: number }[]> = {
        electronics: [
          { name: 'Smartphones', itemCount: 45 },
          { name: 'Laptops', itemCount: 32 },
          { name: 'Headphones', itemCount: 28 },
          { name: 'Cameras', itemCount: 67 },
        ],
        fashion: [
          { name: 'T-Shirts', itemCount: 156 },
          { name: 'Jeans', itemCount: 89 },
          { name: 'Dresses', itemCount: 134 },
          { name: 'Accessories', itemCount: 78 },
        ],
        organic: [
          { name: 'Fruits', itemCount: 45 },
          { name: 'Vegetables', itemCount: 67 },
          { name: 'Grains', itemCount: 34 },
          { name: 'Dairy', itemCount: 23 },
        ],
        home: [
          { name: 'Furniture', itemCount: 34 },
          { name: 'Decor', itemCount: 45 },
          { name: 'Kitchen', itemCount: 67 },
          { name: 'Lighting', itemCount: 23 },
        ],
        smartphones: [
          { name: 'Android', itemCount: 45 },
          { name: 'iPhone', itemCount: 32 },
          { name: 'Accessories', itemCount: 28 },
          { name: 'Cases', itemCount: 15 },
        ],
      };

      const lowerName = categoryName.toLowerCase();
      for (const [key, subs] of Object.entries(subcategoryMap)) {
        if (lowerName.includes(key)) {
          return subs;
        }
      }

      // Default subcategories
      return [
        { name: 'Popular Items', itemCount: 45 },
        { name: 'New Arrivals', itemCount: 32 },
        { name: 'Best Sellers', itemCount: 28 },
        { name: 'On Sale', itemCount: 15 },
      ];
    };

    return {
      id: strapiCategory.handle || strapiCategory.id,
      name: strapiCategory.name,
      description: strapiCategory.description || `Explore our ${strapiCategory.name} collection`,
      image: strapiCategory.image || '/images/placeholder.svg',
      itemCount: Math.floor(Math.random() * 1000) + 100, // Random count for demo
      color,
      subcategories: generateSubcategories(strapiCategory.name),
    };
  };

  // Fetch categories from Strapi CMS
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        console.log('🚀 CategoriesPage: Fetching categories from Strapi CMS...');
        setIsLoading(true);
        setError(null);

        const response = await fetch('/api/categories?parentOnly=true&pageSize=50');

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        console.log('✅ CategoriesPage: API response received:', result);

        if (result.success && result.data && result.data.length > 0) {
          console.log('✅ CategoriesPage: Categories received from Strapi:', result.data);
          const transformedCategories = result.data.map(transformStrapiCategory);
          setCategories(transformedCategories);
          console.log('✅ CategoriesPage: Categories transformed and set:', transformedCategories);
        } else {
          console.log('⚠️ CategoriesPage: No categories found, using default categories');
          setCategories(defaultCategories);
        }
      } catch (error) {
        console.error('❌ CategoriesPage: Error fetching categories:', error);
        console.log('🔄 CategoriesPage: Using default categories due to error');
        setError(error instanceof Error ? error.message : 'Failed to load categories');
        setCategories(defaultCategories);
      } finally {
        console.log('🏁 CategoriesPage: Setting loading to false');
        setIsLoading(false);
      }
    };

    fetchCategories();
  }, []);

  const getGradientClass = (color: string) => {
    const gradients = {
      blue: 'from-blue-500/10 to-blue-600/20',
      pink: 'from-pink-500/10 to-pink-600/20',
      amber: 'from-amber-500/10 to-amber-600/20',
      purple: 'from-purple-500/10 to-purple-600/20',
      green: 'from-green-500/10 to-green-600/20',
      red: 'from-red-500/10 to-red-600/20',
      gray: 'from-gray-500/10 to-gray-600/20',
      orange: 'from-orange-500/10 to-orange-600/20',
    };
    return gradients[color as keyof typeof gradients] || gradients.blue;
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Breadcrumb Navigation */}
      <nav className="flex mb-6" aria-label="Breadcrumb">
        <ol className="inline-flex items-center space-x-1 md:space-x-3">
          <li className="inline-flex items-center">
            <Link
              href="/"
              className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600"
            >
              Home
            </Link>
          </li>
          <li aria-current="page">
            <div className="flex items-center">
              <svg
                className="w-3 h-3 text-gray-400 mx-1"
                aria-hidden="true"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 6 10"
              >
                <path
                  stroke="currentColor"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="m1 9 4-4-4-4"
                />
              </svg>
              <span className="ml-1 text-sm font-medium text-gray-500 md:ml-2">Categories</span>
            </div>
          </li>
        </ol>
      </nav>

      <div className="text-center mb-12">
        <h1 className="text-3xl font-bold text-gray-900">Shop by Category</h1>
        <p className="mt-4 text-lg text-gray-600">
          Explore our wide range of products across various categories
        </p>
        {error && (
          <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p className="text-yellow-800 text-sm">⚠️ Using fallback categories due to: {error}</p>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
        {isLoading
          ? // Show loading skeletons
            Array.from({ length: 8 }).map((_, index) => (
              <CategorySkeleton key={`skeleton-${index}`} />
            ))
          : categories.map(category => (
              <div
                key={category.id}
                className="group relative overflow-hidden rounded-2xl bg-gradient-to-br shadow-sm hover:shadow-lg transition-shadow duration-200 h-80 flex flex-col"
              >
                <Link href={`/categories/${category.slug}`} className="flex flex-col h-full">
                  <div
                    className={`relative overflow-hidden rounded-2xl bg-gradient-to-br ${getGradientClass(category.color)} flex-1 flex flex-col`}
                  >
                    <div className="relative w-full h-40 flex-shrink-0">
                      <Image
                        src={category.image}
                        alt={category.name}
                        fill
                        className="object-cover transition-transform duration-300 group-hover:scale-105"
                        fallback="/images/placeholder.svg"
                      />
                      {/* Enhanced gradient overlay for better text contrast */}
                      <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-black/20"></div>
                    </div>

                    <div className="absolute bottom-0 left-0 right-0 p-6 flex-1 flex flex-col justify-end">
                      <div className="flex items-center justify-between">
                        <div className="flex-1 min-w-0">
                          <h3
                            className="text-xl font-bold text-white mb-2 truncate drop-shadow-lg"
                            title={category.name}
                            style={{ textShadow: '2px 2px 4px rgba(0,0,0,0.8)' }}
                          >
                            {category.name}
                          </h3>
                          <p
                            className="text-white text-sm line-clamp-2 drop-shadow-md"
                            style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }}
                          >
                            {category.description}
                          </p>
                        </div>
                        <div className="bg-white/30 backdrop-blur-sm rounded-full px-3 py-1 border border-white/20">
                          <span
                            className="text-white text-sm font-medium drop-shadow-md"
                            style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }}
                          >
                            {category.itemCount}+ items
                          </span>
                        </div>
                      </div>

                      <div className="mt-4">
                        <div className="flex flex-wrap gap-2">
                          {category.subcategories.map((subcategory, index) => (
                            <span
                              key={index}
                              className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-white/30 text-white backdrop-blur-sm border border-white/20 drop-shadow-sm"
                              style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.6)' }}
                            >
                              {subcategory.name}
                              <span className="ml-1 text-white/90">({subcategory.itemCount})</span>
                            </span>
                          ))}
                        </div>
                      </div>

                      <div
                        className="mt-4 flex items-center text-white text-sm font-medium group-hover:text-white transition-colors duration-200 drop-shadow-md"
                        style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }}
                      >
                        <span>Browse Category</span>
                        <svg
                          className="ml-2 w-4 h-4 transition-transform duration-200 group-hover:translate-x-1 drop-shadow-sm"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 5l7 7-7 7"
                          />
                        </svg>
                      </div>
                    </div>
                  </div>
                </Link>
              </div>
            ))}
      </div>
    </div>
  );
};

export default CategoriesPage;
