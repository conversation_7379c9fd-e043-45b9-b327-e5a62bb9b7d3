'use client';

import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import {
  ArrowLeftIcon,
  FunnelIcon,
  Squares2X2Icon,
  ListBulletIcon,
} from '@heroicons/react/24/outline';
// Remove direct Strapi imports since this is a client component
// import { useCart } from '@/context/CartContext';
import FilterSidebar, { FilterState, defaultFilters } from '@/components/filters/FilterSidebar';

interface Product {
  id: string;
  name: string;
  slug: string;
  price: number;
  originalPrice?: number;
  image: string;
  rating: number;
  reviewCount: number;
  category: string;
  isWishlisted: boolean;
  badge?: string;
}

// Helper function to convert MSW product to category page product
function convertMSWProduct(mswProduct: MockProduct): Product {
  // Check if any variant is out of stock
  const hasOutOfStockVariant = mswProduct.variants?.some(v => !v.available) || false;
  const hasNewTag = mswProduct.tags?.includes('new') || false;
  const hasProTag = mswProduct.tags?.includes('pro') || false;

  let badge = undefined;
  if (hasOutOfStockVariant) badge = 'Out of Stock';
  else if (hasProTag) badge = 'Pro';
  else if (hasNewTag) badge = 'New';
  else if (mswProduct.reviews && mswProduct.reviews > 300) badge = 'Best Seller';

  return {
    id: mswProduct.id,
    name: mswProduct.title,
    slug: mswProduct.handle,
    price: mswProduct.price / 100, // Convert from cents to dollars
    originalPrice: mswProduct.price > 2000 ? (mswProduct.price * 1.25) / 100 : undefined, // Add original price for expensive items
    image: mswProduct.thumbnail,
    rating: mswProduct.rating || 4.0,
    reviewCount: mswProduct.reviews || 0,
    category: mswProduct.category || '',
    isWishlisted: false,
    badge,
  };
}

interface Category {
  id: number;
  name: string;
  slug: string;
  image: string;
  productCount: number;
  description: string;
}

interface Subcategory {
  id: string;
  name: string;
  description: string;
  slug: string;
  parent: Category;
}

// Category slug to MSW category mapping
const categorySlugMap: { [key: string]: string } = {
  electronics: 'electronics',
  fashion: 'fashion',
  'home-garden': 'home-garden',
  'sports-fitness': 'sports-fitness',
  'books-media': 'books-media',
  'beauty-health': 'beauty-health',
};

const categoryMap: { [key: string]: Category } = {
  electronics: {
    id: 1,
    name: 'Electronics',
    slug: 'electronics',
    image: '/images/categories/electronics.jpg',
    productCount: 1250,
    description: 'Latest gadgets and tech',
  },
  fashion: {
    id: 2,
    name: 'Fashion',
    slug: 'fashion',
    image: '/images/categories/fashion.jpg',
    productCount: 2100,
    description: 'Trendy clothing & accessories',
  },
  'home-garden': {
    id: 3,
    name: 'Home & Garden',
    slug: 'home-garden',
    image: '/images/categories/home-garden.jpg',
    productCount: 890,
    description: 'Furniture & home decor',
  },
  'sports-fitness': {
    id: 4,
    name: 'Sports & Fitness',
    slug: 'sports-fitness',
    image: '/images/categories/sports-fitness.jpg',
    productCount: 650,
    description: 'Athletic gear & equipment',
  },
  'books-media': {
    id: 5,
    name: 'Books & Media',
    slug: 'books-media',
    image: '/images/categories/books-media.jpg',
    productCount: 1800,
    description: 'Books, movies & music',
  },
  'beauty-health': {
    id: 6,
    name: 'Beauty & Health',
    slug: 'beauty-health',
    image: '/images/categories/beauty-health.jpg',
    productCount: 750,
    description: 'Skincare & wellness',
  },
  automotive: {
    id: 7,
    name: 'Automotive',
    slug: 'automotive',
    image: '/images/categories/automotive.jpg',
    productCount: 420,
    description: 'Car parts & accessories',
  },
  'toys-games': {
    id: 8,
    name: 'Toys & Games',
    slug: 'toys-games',
    image: '/images/categories/toys.jpg',
    productCount: 980,
    description: 'Fun for all ages',
  },
};

export default function CategoryPage() {
  const params = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  // const { addItem } = useCart();

  const slug = params?.slug as string;
  console.log('🔍 CategoryPage: Component mounted, slug value:', slug, typeof slug);

  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState('featured');
  const [isLoading, setIsLoading] = useState(true);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [filters, setFilters] = useState<FilterState>(defaultFilters);
  const [category, setCategory] = useState<Category | null>(null);
  const [products, setProducts] = useState<Product[]>([]);
  const [subcategories, setSubcategories] = useState<Subcategory[]>([]);
  const [selectedSubcategory, setSelectedSubcategory] = useState<string | null>(null);
  const [allProducts, setAllProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);

  // TEST: Set some test products directly to see if display works
  React.useEffect(() => {
    console.log('🧪 TEST: Setting test products directly');
    const testProducts = getTestProducts('electronics');
    setProducts(testProducts);
    setAllProducts(testProducts);
    setFilteredProducts(testProducts);
    console.log('🧪 TEST: Set', testProducts.length, 'test products');
  }, []);

  // Fetch category and products from Strapi
  useEffect(() => {
    console.log('🎯🎯🎯 CategoryPage: useEffect STARTING for slug:', slug);
    console.log('🎯🎯🎯 CategoryPage: useEffect DEFINITELY CALLED!');
    const fetchCategoryData = async () => {
      try {
        console.log('🚀 CategoryPage: Fetching category data for slug:', slug);
        setIsLoading(true);

        // Fetch category details from API route
        console.log('🔍 CategoryPage: About to fetch category from API...');
        const categoryResponse = await fetch(`/api/categories/${slug}`);
        console.log('✅ CategoryPage: Category API response status:', categoryResponse.status);

        let categoryData = null;
        if (categoryResponse.ok) {
          const categoryResult = await categoryResponse.json();
          console.log('✅ CategoryPage: Category API result:', categoryResult);
          if (categoryResult.success) {
            categoryData = categoryResult.data;
            console.log('✅ CategoryPage: Category data received:', categoryData);
            console.log('✅ CategoryPage: Category ID:', categoryData?.id);
            console.log('✅ CategoryPage: Category name:', categoryData?.name);
          } else {
            console.error('❌ CategoryPage: Category API returned success=false');
          }
        } else {
          console.error('❌ CategoryPage: Category API response not ok:', categoryResponse.status);
        }

        if (categoryData) {
          // Transform Strapi category data
          const transformedCategory: Category = {
            id: categoryData.id,
            name: categoryData.name || categoryData.attributes?.name || 'Unknown Category',
            slug: categoryData.slug || categoryData.attributes?.slug || slug,
            image: `/images/categories/${slug}.jpg`, // Use slug-based image
            productCount: Math.floor(Math.random() * 2000) + 100, // Random count for now
            description:
              extractTextFromRichText(
                categoryData.description || categoryData.attributes?.description
              ) || 'Explore our products',
          };
          setCategory(transformedCategory);

          // Fetch subcategories for this parent category
          try {
            const subcategoriesResponse = await fetch(`/api/subcategories/${categoryData.id}`);
            if (subcategoriesResponse.ok) {
              const subcategoriesResult = await subcategoriesResponse.json();
              if (subcategoriesResult.success && subcategoriesResult.data) {
                setSubcategories(subcategoriesResult.data);
                console.log('✅ CategoryPage: Subcategories loaded:', subcategoriesResult.data);
              }
            }
          } catch (error) {
            console.log('⚠️ CategoryPage: Could not fetch subcategories:', error);
          }

          // Fetch products for this category (including all subcategories) via API
          console.log('🔍 CategoryPage: About to fetch products from API...');
          console.log('🔍 CategoryPage: Category ID for products fetch:', categoryData.id);
          const productsApiResponse = await fetch(
            `/api/products/by-category?categoryId=${categoryData.id}&pageSize=100`
          );
          console.log('✅ CategoryPage: Products API response status:', productsApiResponse.status);

          let productsResponse = { data: [], meta: {} };
          if (productsApiResponse.ok) {
            const productsResult = await productsApiResponse.json();
            console.log('✅ CategoryPage: Products API result:', productsResult);
            if (productsResult.success) {
              productsResponse = {
                data: productsResult.data || [],
                meta: productsResult.meta || {},
              };
              console.log(
                '✅ CategoryPage: Products data received:',
                productsResponse.data.length,
                'products'
              );
            }
          }

          if (productsResponse.data && productsResponse.data.length > 0) {
            const transformedProducts = productsResponse.data.map(transformStrapiProduct);
            setAllProducts(transformedProducts);
            setFilteredProducts(transformedProducts);
            setProducts(transformedProducts);
          } else {
            // Use fallback products if no Strapi data
            const fallbackProducts = getTestProducts(slug);
            setAllProducts(fallbackProducts);
            setFilteredProducts(fallbackProducts);
            setProducts(fallbackProducts);
          }
        } else {
          // Use fallback category if not found in Strapi
          const fallbackCategory = categoryMap[slug];
          if (fallbackCategory) {
            setCategory(fallbackCategory);
            const fallbackProducts = getTestProducts(slug);
            setAllProducts(fallbackProducts);
            setFilteredProducts(fallbackProducts);
            setProducts(fallbackProducts);
          }
        }
      } catch (error) {
        console.error('❌ CategoryPage: Error fetching category data:', error);
        // Use fallback data on error
        const fallbackCategory = categoryMap[slug];
        if (fallbackCategory) {
          setCategory(fallbackCategory);
          const fallbackProducts = getTestProducts(slug);
          setAllProducts(fallbackProducts);
          setFilteredProducts(fallbackProducts);
          setProducts(fallbackProducts);
        }
      } finally {
        setIsLoading(false);
      }
    };

    if (slug) {
      fetchCategoryData();
    }
  }, [slug]);

  // Handle subcategory filtering
  const handleSubcategoryFilter = async (subcategoryId: string | null) => {
    console.log('🎯 CategoryPage: Filtering by subcategory:', subcategoryId);
    setSelectedSubcategory(subcategoryId);
    setIsLoading(true);

    try {
      if (subcategoryId === null) {
        // Show all products
        setFilteredProducts(allProducts);
        setProducts(allProducts);
      } else {
        // Filter products by subcategory
        const response = await fetch(
          `/api/products/by-category?subcategoryId=${subcategoryId}&pageSize=100`
        );
        if (response.ok) {
          const result = await response.json();
          if (result.success && result.data) {
            const transformedProducts = result.data.map(transformStrapiProduct);
            setFilteredProducts(transformedProducts);
            setProducts(transformedProducts);
            console.log('✅ CategoryPage: Filtered products loaded:', transformedProducts.length);
          }
        } else {
          // Fallback: filter from all products (for test data)
          const filtered = allProducts.filter(product =>
            product.category
              .toLowerCase()
              .includes(
                subcategories.find(sub => sub.id === subcategoryId)?.name.toLowerCase() || ''
              )
          );
          setFilteredProducts(filtered);
          setProducts(filtered);
        }
      }
    } catch (error) {
      console.error('❌ CategoryPage: Error filtering by subcategory:', error);
      // Fallback to showing all products
      setFilteredProducts(allProducts);
      setProducts(allProducts);
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to extract text from rich text content
  function extractTextFromRichText(richText: any): string {
    if (!richText) return '';

    if (Array.isArray(richText)) {
      return richText
        .map((block: any) => {
          if (block.children && Array.isArray(block.children)) {
            return block.children.map((child: any) => child.text || '').join('');
          }
          return '';
        })
        .join(' ');
    }

    if (typeof richText === 'string') {
      return richText;
    }

    return '';
  }

  // Helper function to transform Strapi product data
  function transformStrapiProduct(strapiProduct: any): Product {
    const attributes = strapiProduct.attributes || strapiProduct;
    const name = attributes.name || strapiProduct.name || 'Unknown Product';
    const price = attributes.price || strapiProduct.price || 0;
    const salePrice = attributes.sale_price || strapiProduct.sale_price;

    return {
      id: strapiProduct.id.toString(),
      name,
      slug: attributes.slug || name.toLowerCase().replace(/\s+/g, '-'),
      price: salePrice || price,
      originalPrice: salePrice ? price : undefined,
      image: '/images/products/placeholder.svg', // Default image for now
      rating: 4.0 + Math.random() * 1, // Random rating between 4-5
      reviewCount: Math.floor(Math.random() * 300) + 10,
      category: category?.name || 'Unknown',
      isWishlisted: false,
      badge: attributes.featured || strapiProduct.featured ? 'Featured' : undefined,
    };
  }

  // Hardcoded products for immediate testing
  const getTestProducts = (categorySlug: string): Product[] => {
    const testProducts = {
      electronics: [
        {
          id: 'test-electronics-1',
          name: 'Premium Wireless Headphones',
          slug: 'premium-wireless-headphones',
          price: 299.99,
          originalPrice: 399.99,
          image: '/images/products/headphones.jpg',
          rating: 4.8,
          reviewCount: 324,
          category: 'Electronics',
          isWishlisted: false,
          badge: 'Best Seller',
        },
        {
          id: 'test-electronics-2',
          name: 'Smart Fitness Watch',
          slug: 'smart-fitness-watch',
          price: 199.99,
          image: '/images/products/smartwatch.jpg',
          rating: 4.6,
          reviewCount: 156,
          category: 'Electronics',
          isWishlisted: false,
        },
        {
          id: 'test-electronics-3',
          name: 'Professional Camera Lens',
          slug: 'professional-camera-lens',
          price: 599.99,
          originalPrice: 699.99,
          image: '/images/products/camera-lens.jpg',
          rating: 4.9,
          reviewCount: 89,
          category: 'Electronics',
          isWishlisted: false,
          badge: 'Pro',
        },
        {
          id: 'test-electronics-4',
          name: 'Gaming Mechanical Keyboard',
          slug: 'gaming-mechanical-keyboard',
          price: 149.99,
          image: '/images/products/placeholder.svg',
          rating: 4.7,
          reviewCount: 203,
          category: 'Electronics',
          isWishlisted: false,
        },
      ],
      fashion: [
        {
          id: 'test-fashion-1',
          name: 'Organic Cotton T-Shirt',
          slug: 'organic-cotton-t-shirt',
          price: 29.99,
          image: '/images/products/tshirt.jpg',
          rating: 4.5,
          reviewCount: 127,
          category: 'Fashion',
          isWishlisted: false,
          badge: 'Eco-Friendly',
        },
        {
          id: 'test-fashion-2',
          name: 'Designer Denim Jeans',
          slug: 'designer-denim-jeans',
          price: 89.99,
          originalPrice: 119.99,
          image: '/images/products/placeholder.svg',
          rating: 4.4,
          reviewCount: 98,
          category: 'Fashion',
          isWishlisted: false,
        },
        {
          id: 'test-fashion-3',
          name: 'Casual Summer Dress',
          slug: 'casual-summer-dress',
          price: 59.99,
          image: '/images/products/placeholder.svg',
          rating: 4.6,
          reviewCount: 145,
          category: 'Fashion',
          isWishlisted: false,
        },
        {
          id: 'test-fashion-4',
          name: 'Leather Crossbody Bag',
          slug: 'leather-crossbody-bag',
          price: 79.99,
          originalPrice: 99.99,
          image: '/images/products/placeholder.svg',
          rating: 4.8,
          reviewCount: 67,
          category: 'Fashion',
          isWishlisted: false,
          badge: 'Trending',
        },
      ],
      'home-garden': [
        {
          id: 'test-home-1',
          name: 'Premium Coffee Maker',
          slug: 'premium-coffee-maker',
          price: 199.99,
          originalPrice: 249.99,
          image: '/images/products/coffee-maker.jpg',
          rating: 4.7,
          reviewCount: 156,
          category: 'Home & Garden',
          isWishlisted: false,
          badge: 'Best Seller',
        },
        {
          id: 'test-home-2',
          name: 'Ergonomic Office Chair',
          slug: 'ergonomic-office-chair',
          price: 299.99,
          image: '/images/products/office-chair.jpg',
          rating: 4.6,
          reviewCount: 89,
          category: 'Home & Garden',
          isWishlisted: false,
        },
        {
          id: 'test-home-3',
          name: 'LED Desk Lamp',
          slug: 'led-desk-lamp',
          price: 49.99,
          image: '/images/products/placeholder.svg',
          rating: 4.4,
          reviewCount: 234,
          category: 'Home & Garden',
          isWishlisted: false,
        },
        {
          id: 'test-home-4',
          name: 'Bamboo Cutting Board Set',
          slug: 'bamboo-cutting-board-set',
          price: 34.99,
          originalPrice: 44.99,
          image: '/images/products/placeholder.svg',
          rating: 4.8,
          reviewCount: 178,
          category: 'Home & Garden',
          isWishlisted: false,
          badge: 'Eco-Friendly',
        },
      ],
      'sports-fitness': [
        {
          id: 'test-sports-1',
          name: 'Premium Yoga Mat',
          slug: 'premium-yoga-mat',
          price: 49.99,
          originalPrice: 69.99,
          image: '/images/products/placeholder.svg',
          rating: 4.8,
          reviewCount: 234,
          category: 'Sports & Fitness',
          isWishlisted: false,
          badge: 'Best Seller',
        },
        {
          id: 'test-sports-2',
          name: 'Adjustable Dumbbells Set',
          slug: 'adjustable-dumbbells-set',
          price: 149.99,
          image: '/images/products/placeholder.svg',
          rating: 4.5,
          reviewCount: 167,
          category: 'Sports & Fitness',
          isWishlisted: false,
        },
        {
          id: 'test-sports-3',
          name: 'Resistance Bands Kit',
          slug: 'resistance-bands-kit',
          price: 24.99,
          image: '/images/products/placeholder.svg',
          rating: 4.6,
          reviewCount: 312,
          category: 'Sports & Fitness',
          isWishlisted: false,
        },
        {
          id: 'test-sports-4',
          name: 'Bluetooth Sports Headphones',
          slug: 'bluetooth-sports-headphones',
          price: 79.99,
          originalPrice: 99.99,
          image: '/images/products/headphones.jpg',
          rating: 4.4,
          reviewCount: 189,
          category: 'Sports & Fitness',
          isWishlisted: false,
        },
      ],
      'books-media': [
        {
          id: 'test-books-1',
          name: 'Programming Fundamentals Book',
          slug: 'programming-fundamentals-book',
          price: 39.99,
          originalPrice: 49.99,
          image: '/images/products/placeholder.svg',
          rating: 4.9,
          reviewCount: 345,
          category: 'Books & Media',
          isWishlisted: false,
          badge: 'Bestseller',
        },
        {
          id: 'test-books-2',
          name: 'Wireless Earbuds',
          slug: 'wireless-earbuds',
          price: 79.99,
          image: '/images/products/placeholder.svg',
          rating: 4.3,
          reviewCount: 123,
          category: 'Books & Media',
          isWishlisted: false,
        },
        {
          id: 'test-books-3',
          name: 'Digital Photography Guide',
          slug: 'digital-photography-guide',
          price: 29.99,
          image: '/images/products/placeholder.svg',
          rating: 4.7,
          reviewCount: 267,
          category: 'Books & Media',
          isWishlisted: false,
        },
        {
          id: 'test-books-4',
          name: 'Bluetooth Speaker',
          slug: 'bluetooth-speaker',
          price: 59.99,
          originalPrice: 79.99,
          image: '/images/products/placeholder.svg',
          rating: 4.5,
          reviewCount: 198,
          category: 'Books & Media',
          isWishlisted: false,
        },
      ],
      'beauty-health': [
        {
          id: 'test-beauty-1',
          name: 'Vitamin D3 Supplements',
          slug: 'vitamin-d3-supplements',
          price: 24.99,
          originalPrice: 29.99,
          image: '/images/products/placeholder.svg',
          rating: 4.6,
          reviewCount: 189,
          category: 'Beauty & Health',
          isWishlisted: false,
          badge: 'Health',
        },
        {
          id: 'test-beauty-2',
          name: 'Moisturizing Face Cream',
          slug: 'moisturizing-face-cream',
          price: 34.99,
          image: '/images/products/placeholder.svg',
          rating: 4.4,
          reviewCount: 267,
          category: 'Beauty & Health',
          isWishlisted: false,
        },
        {
          id: 'test-beauty-3',
          name: 'Organic Shampoo & Conditioner',
          slug: 'organic-shampoo-conditioner',
          price: 19.99,
          image: '/images/products/placeholder.svg',
          rating: 4.7,
          reviewCount: 156,
          category: 'Beauty & Health',
          isWishlisted: false,
          badge: 'Organic',
        },
        {
          id: 'test-beauty-4',
          name: 'Essential Oils Starter Kit',
          slug: 'essential-oils-starter-kit',
          price: 49.99,
          originalPrice: 64.99,
          image: '/images/products/placeholder.svg',
          rating: 4.8,
          reviewCount: 89,
          category: 'Beauty & Health',
          isWishlisted: false,
          badge: 'Aromatherapy',
        },
      ],
    };
    return testProducts[categorySlug as keyof typeof testProducts] || [];
  };

  // Filter products based on current filters
  const displayProducts = products.filter(product => {
    // Price filter
    if (product.price < filters.priceRange.min || product.price > filters.priceRange.max) {
      return false;
    }

    // Brand filter (mock brand extraction from product name)
    if (filters.brands.length > 0) {
      const productBrand = product.name.split(' ')[0]; // Simple brand extraction
      if (!filters.brands.includes(productBrand)) {
        return false;
      }
    }

    // Rating filter
    if (filters.rating && product.rating < filters.rating) {
      return false;
    }

    // Availability filter
    if (filters.availability === 'in-stock' && product.badge === 'Out of Stock') {
      return false;
    }
    if (filters.availability === 'out-of-stock' && product.badge !== 'Out of Stock') {
      return false;
    }

    return true;
  });

  // Sort products
  const sortedProducts = [...displayProducts].sort((a, b) => {
    switch (sortBy) {
      case 'price-low':
        return a.price - b.price;
      case 'price-high':
        return b.price - a.price;
      case 'rating':
        return b.rating - a.rating;
      case 'newest':
        return b.id.localeCompare(a.id);
      case 'bestseller':
        return b.reviewCount - a.reviewCount;
      default:
        return 0;
    }
  });

  const finalDisplayProducts = sortedProducts;

  // Add to cart functionality
  const handleAddToCart = (product: Product) => {
    console.log('Add to cart:', product.name);
    // TODO: Implement cart functionality
    // addItem({
    //   id: product.id,
    //   productId: product.id,
    //   name: product.name,
    //   price: product.price,
    //   image: product.image,
    //   sellerId: 'default-seller',
    //   sellerName: 'ONDC Seller',
    //   maxQuantity: 10,
    // });
  };

  console.log('CategoryPage rendering for slug:', slug);
  console.log('Products available:', finalDisplayProducts.length);
  console.log('Filters applied:', filters);

  if (!category) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Category Not Found</h1>
          <p className="text-gray-600 mb-8">The category you're looking for doesn't exist.</p>
          <Link
            href="/categories"
            className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            <ArrowLeftIcon className="h-5 w-5 mr-2" />
            Back to Categories
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Filter Sidebar */}
      <FilterSidebar
        isOpen={isFilterOpen}
        onClose={() => setIsFilterOpen(false)}
        filters={filters}
        onFiltersChange={setFilters}
      />

      {/* Category Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center space-x-6">
            <div className="relative w-24 h-24 rounded-lg overflow-hidden">
              <Image
                src={category.image}
                alt={category.name}
                fill
                className="object-cover"
                sizes="96px"
                onError={e => {
                  const target = e.target as HTMLImageElement;
                  target.src = '/images/placeholder.svg';
                }}
              />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">{category.name}</h1>
              <p className="text-lg text-gray-600 mb-2">{category.description}</p>
              <p className="text-sm text-gray-500">
                {category.productCount.toLocaleString()} products available
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Filters and Controls */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Breadcrumb Navigation */}
        <nav className="flex mb-6" aria-label="Breadcrumb">
          <ol className="inline-flex items-center space-x-1 md:space-x-3">
            <li className="inline-flex items-center">
              <Link
                href="/"
                className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600"
              >
                Home
              </Link>
            </li>
            <li>
              <div className="flex items-center">
                <svg
                  className="w-3 h-3 text-gray-400 mx-1"
                  aria-hidden="true"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 6 10"
                >
                  <path
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="m1 9 4-4-4-4"
                  />
                </svg>
                <Link
                  href="/categories"
                  className="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2"
                >
                  Categories
                </Link>
              </div>
            </li>
            <li aria-current="page">
              <div className="flex items-center">
                <svg
                  className="w-3 h-3 text-gray-400 mx-1"
                  aria-hidden="true"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 6 10"
                >
                  <path
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="m1 9 4-4-4-4"
                  />
                </svg>
                <span className="ml-1 text-sm font-medium text-gray-500 md:ml-2">
                  {category.name}
                </span>
              </div>
            </li>
          </ol>
        </nav>

        {/* Subcategory Filter Badges */}
        {subcategories.length > 0 && (
          <div className="mb-6">
            <h3 className="text-sm font-medium text-gray-700 mb-3">Filter by subcategory:</h3>
            <div className="flex flex-wrap gap-2">
              {/* All Categories Badge */}
              <button
                onClick={() => handleSubcategoryFilter(null)}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
                  selectedSubcategory === null
                    ? 'bg-blue-600 text-white shadow-md'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200 border border-gray-300'
                }`}
              >
                All ({allProducts.length})
              </button>

              {/* Individual Subcategory Badges */}
              {subcategories.map(subcategory => (
                <button
                  key={subcategory.id}
                  onClick={() => handleSubcategoryFilter(subcategory.id)}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
                    selectedSubcategory === subcategory.id
                      ? 'bg-blue-600 text-white shadow-md'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200 border border-gray-300'
                  }`}
                >
                  {subcategory.name}
                </button>
              ))}
            </div>
          </div>
        )}

        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6 space-y-4 lg:space-y-0">
          <div className="flex flex-wrap items-center gap-4">
            <button
              onClick={() => setIsFilterOpen(true)}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors shadow-sm"
            >
              <FunnelIcon className="h-4 w-4 mr-2" />
              Filters
              {(filters.brands.length > 0 ||
                filters.rating ||
                filters.availability !== 'all' ||
                filters.priceRange.min > 0 ||
                filters.priceRange.max < 1000) && (
                <span className="ml-2 bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                  {filters.brands.length +
                    (filters.rating ? 1 : 0) +
                    (filters.availability !== 'all' ? 1 : 0) +
                    (filters.priceRange.min > 0 || filters.priceRange.max < 1000 ? 1 : 0)}
                </span>
              )}
            </button>

            {/* Enhanced Sort Dropdown */}
            <div className="relative">
              <select
                value={sortBy}
                onChange={e => setSortBy(e.target.value)}
                className="appearance-none border border-gray-300 rounded-lg px-4 py-2 pr-8 text-sm bg-white hover:bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
              >
                <option value="featured">Featured</option>
                <option value="price-low">Price: Low to High</option>
                <option value="price-high">Price: High to Low</option>
                <option value="rating">Highest Rated</option>
                <option value="newest">Newest</option>
                <option value="bestseller">Best Sellers</option>
              </select>
              <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                <svg
                  className="fill-current h-4 w-4"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                >
                  <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                </svg>
              </div>
            </div>

            {/* Results Count */}
            <span className="text-sm text-gray-600">
              Showing {finalDisplayProducts.length} of {filteredProducts.length} products
              {selectedSubcategory && (
                <span className="text-blue-600 font-medium">
                  {' '}
                  in {subcategories.find(sub => sub.id === selectedSubcategory)?.name}
                </span>
              )}
            </span>
          </div>

          <div className="flex items-center space-x-3">
            {/* View Mode Toggle */}
            <div className="flex items-center bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded-md transition-colors ${
                  viewMode === 'grid'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
                title="Grid View"
              >
                <Squares2X2Icon className="h-5 w-5" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded-md transition-colors ${
                  viewMode === 'list'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
                title="List View"
              >
                <ListBulletIcon className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>

        {/* Products Grid/List */}
        {isLoading ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {[...Array(8)].map((_, index) => (
              <div key={index} className="bg-white rounded-lg p-4 animate-pulse">
                <div className="bg-gray-200 rounded-lg h-48 mb-4"></div>
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-2/3"></div>
              </div>
            ))}
          </div>
        ) : (
          <div
            className={
              viewMode === 'grid'
                ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
                : 'space-y-4'
            }
          >
            {finalDisplayProducts.map(product => (
              <div
                key={product.id}
                className={`group bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 border border-gray-100 hover:border-gray-200 ${
                  viewMode === 'list'
                    ? 'flex items-center p-4'
                    : 'p-4 h-full flex flex-col overflow-hidden'
                }`}
              >
                <div
                  className={`relative ${viewMode === 'list' ? 'w-24 h-24 mr-4' : 'aspect-square mb-4'} overflow-hidden rounded-lg flex-shrink-0`}
                >
                  <Image
                    src={product.image}
                    alt={product.name}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                    sizes={
                      viewMode === 'list'
                        ? '96px'
                        : '(max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw'
                    }
                    onError={e => {
                      const target = e.target as HTMLImageElement;
                      target.src = '/images/products/placeholder.svg';
                    }}
                  />
                  {product.badge && (
                    <div className="absolute top-2 left-2 bg-gradient-to-r from-blue-600 to-blue-700 text-white text-xs px-2 py-1 rounded-full font-medium shadow-sm">
                      {product.badge}
                    </div>
                  )}

                  {/* Wishlist Button */}
                  <button
                    className="absolute top-2 right-2 p-2 bg-white/80 hover:bg-white rounded-full shadow-sm opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                    title="Add to Wishlist"
                  >
                    <svg
                      className="h-4 w-4 text-gray-600 hover:text-red-500 transition-colors"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                      />
                    </svg>
                  </button>

                  {/* Quick View Button */}
                  <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
                    <button className="bg-white text-gray-900 px-4 py-2 rounded-lg font-medium text-sm hover:bg-gray-50 transition-colors shadow-lg">
                      Quick View
                    </button>
                  </div>
                </div>

                <div className={viewMode === 'list' ? 'flex-1' : 'flex flex-col flex-1'}>
                  {/* Enhanced Product Title */}
                  <div className="mb-3">
                    <Link href={`/products/${product.slug}`} className="group">
                      <h3
                        className="font-semibold text-gray-900 line-clamp-2 text-sm leading-tight group-hover:text-blue-600 transition-colors"
                        title={product.name}
                      >
                        {product.name}
                      </h3>
                    </Link>
                  </div>

                  {/* Enhanced Rating Display */}
                  <div className="flex items-center mb-3">
                    <div className="flex items-center">
                      {[...Array(5)].map((_, i) => (
                        <svg
                          key={i}
                          className={`h-4 w-4 ${i < Math.floor(product.rating) ? 'text-yellow-400' : 'text-gray-300'}`}
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                      ))}
                    </div>
                    <span className="text-sm text-gray-500 ml-2 font-medium">
                      {product.rating} ({product.reviewCount})
                    </span>
                  </div>

                  {/* Enhanced Price and Action Container */}
                  <div className="mt-auto space-y-3">
                    {/* Price Section */}
                    <div className="flex flex-col">
                      <div className="flex items-center space-x-2">
                        <span className="text-lg font-bold text-gray-900">${product.price}</span>
                        {product.originalPrice && (
                          <span className="text-sm text-gray-500 line-through">
                            ${product.originalPrice}
                          </span>
                        )}
                      </div>
                      {product.originalPrice && (
                        <span className="text-xs text-green-600 font-medium">
                          Save ${(product.originalPrice - product.price).toFixed(2)}
                        </span>
                      )}
                    </div>

                    {/* Action Buttons */}
                    <div className="flex items-center space-x-2">
                      {/* Add to Cart Button */}
                      <button
                        onClick={() => handleAddToCart(product)}
                        className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors shadow-sm hover:shadow-md flex items-center justify-center space-x-1"
                        title="Add to Cart"
                      >
                        <svg
                          className="h-4 w-4"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 11-4 0v-6m4 0V9a2 2 0 10-4 0v4.01"
                          />
                        </svg>
                        <span className="hidden sm:inline">Add</span>
                      </button>

                      {/* View Details Link */}
                      <Link
                        href={`/products/${product.slug}`}
                        className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-2 rounded-lg text-sm font-medium transition-colors border border-gray-200 hover:border-gray-300 text-center"
                      >
                        View
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Pagination - Only show if there are products */}
        {finalDisplayProducts.length > 0 && (
          <div className="flex items-center justify-center mt-12">
            <nav className="flex items-center space-x-2">
              <button className="px-3 py-2 text-sm text-gray-500 hover:text-gray-700">
                Previous
              </button>
              <button className="px-3 py-2 text-sm bg-blue-600 text-white rounded">1</button>
              <button className="px-3 py-2 text-sm text-gray-500 hover:text-gray-700">2</button>
              <button className="px-3 py-2 text-sm text-gray-500 hover:text-gray-700">3</button>
              <button className="px-3 py-2 text-sm text-gray-500 hover:text-gray-700">Next</button>
            </nav>
          </div>
        )}

        {/* No Products Message */}
        {!isLoading && finalDisplayProducts.length === 0 && (
          <div className="text-center py-12">
            <div className="max-w-md mx-auto">
              <div className="mb-4">
                <svg
                  className="mx-auto h-12 w-12 text-gray-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2 2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H6a1 1 0 00-1 1v1m16 0V4a1 1 0 00-1-1H6a1 1 0 00-1 1v1"
                  />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No products found</h3>
              <p className="text-gray-500 mb-6">
                {selectedSubcategory
                  ? `No products available in the selected subcategory.`
                  : `No products available in this category.`}
              </p>
              {selectedSubcategory && (
                <button
                  onClick={() => handleSubcategoryFilter(null)}
                  className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  View All Products
                </button>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
