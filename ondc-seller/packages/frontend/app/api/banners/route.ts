import { NextRequest, NextResponse } from 'next/server';

/**
 * GET /api/banners
 * 
 * Fetch banner slides for the homepage hero section.
 * Returns mock data since Strapi banners are not currently populated.
 */
export async function GET(request: NextRequest) {
  try {
    console.log('🚀 [API] /api/banners - Fetching banner slides...');

    // Mock banner data that matches the expected format
    const mockBanners = [
      {
        id: 1,
        title: 'Summer Sale 2024',
        subtitle: 'Up to 70% Off',
        description: 'Discover amazing deals on electronics, fashion, and home essentials',
        image: '/images/banners/summer-sale.jpg',
        buttonText: 'Shop Now',
        buttonLink: '/products',
        backgroundColor: 'bg-gradient-to-r from-blue-600 to-purple-600',
        active: true,
        position: 1,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: 2,
        title: 'New Arrivals',
        subtitle: 'Fresh Collection',
        description: 'Explore the latest trends in fashion and lifestyle products',
        image: '/images/banners/new-arrivals.jpg',
        buttonText: 'Explore',
        buttonLink: '/categories',
        backgroundColor: 'bg-gradient-to-r from-green-500 to-teal-600',
        active: true,
        position: 2,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: 3,
        title: 'Electronics Mega Sale',
        subtitle: 'Best Prices Guaranteed',
        description: 'Latest smartphones, laptops, and gadgets at unbeatable prices',
        image: '/images/banners/electronics-sale.jpg',
        buttonText: 'Shop Electronics',
        buttonLink: '/categories/electronics',
        backgroundColor: 'bg-gradient-to-r from-orange-500 to-red-600',
        active: true,
        position: 3,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: 4,
        title: 'Home & Garden',
        subtitle: 'Transform Your Space',
        description: 'Beautiful furniture and decor to make your house a home',
        image: '/images/banners/home-garden.jpg',
        buttonText: 'Shop Home',
        buttonLink: '/categories/home-garden',
        backgroundColor: 'bg-gradient-to-r from-emerald-500 to-cyan-600',
        active: true,
        position: 4,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: 5,
        title: 'Fashion Forward',
        subtitle: 'Style That Speaks',
        description: 'Trendy clothing and accessories for every occasion',
        image: '/images/banners/fashion.jpg',
        buttonText: 'Shop Fashion',
        buttonLink: '/categories/fashion',
        backgroundColor: 'bg-gradient-to-r from-pink-500 to-rose-600',
        active: true,
        position: 5,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    ];

    console.log('✅ [API] /api/banners - Returning mock banner data:', mockBanners.length, 'banners');

    return NextResponse.json({
      success: true,
      data: mockBanners,
      meta: {
        total: mockBanners.length,
        page: 1,
        pageSize: mockBanners.length,
        pageCount: 1,
      },
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('❌ [API] /api/banners - Error:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch banners',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, { status: 500 });
  }
}
