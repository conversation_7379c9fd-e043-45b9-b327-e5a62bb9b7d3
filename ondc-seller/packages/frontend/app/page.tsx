'use client';

import React, { useEffect, useState } from 'react';
import HeroBanner from '@/components/homepage/HeroBanner';
import ShopByCategory from '@/components/homepage/ShopByCategory';
import FeaturedProducts from '@/components/homepage/FeaturedProducts';
import PopularDeals from '@/components/homepage/PopularDeals';
import { getCategories, getProducts } from '@/lib/strapi-api';

export default function HomePage() {
  const [pageKey, setPageKey] = useState(0);
  const [authData, setAuthData] = useState<{ user: any; isAuthenticated: boolean; totalItems: number }>({
    user: null,
    isAuthenticated: false,
    totalItems: 0,
  });

  useEffect(() => {
    // Force re-render when component mounts
    console.log('HomePage component mounted/re-mounted');
    setPageKey(prev => prev + 1);
  }, []);

  // Listen for route changes and refresh banner
  useEffect(() => {
    const handleRouteChange = () => {
      console.log('Route changed, refreshing banner...');
      setPageKey(prev => prev + 1);
    };

    // Listen for browser navigation events
    window.addEventListener('popstate', handleRouteChange);

    return () => {
      window.removeEventListener('popstate', handleRouteChange);
    };
  }, []);

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Banner Carousel */}
      <HeroBanner key={`hero-banner-${pageKey}`} />

      {/* Shop by Category Section */}
      <ShopByCategory />

      {/* Featured Products Section */}
      <FeaturedProducts />

      {/* Popular Deals Section */}
      <PopularDeals />

      {/* Welcome Message for Authenticated Users */}
      {isAuthenticated && (
        <div className="bg-blue-50 border-l-4 border-blue-400 py-8">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex">
              <div className="ml-3">
                <p className="text-sm text-blue-700">
                  Welcome back, {user?.name || 'User'}! You have {totalItems} item(s) in your cart.
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
