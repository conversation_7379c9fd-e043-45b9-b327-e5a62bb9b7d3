'use client';

import React, { useState, useEffect } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, Autoplay } from 'swiper/modules';
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';

interface BannerSlide {
  id: number;
  title: string;
  subtitle: string;
  description: string;
  image: string;
  buttonText: string;
  buttonLink: string;
  backgroundColor: string;
}

const defaultSlides: BannerSlide[] = [
  {
    id: 1,
    title: 'Summer Sale 2024',
    subtitle: 'Up to 70% Off',
    description: 'Discover amazing deals on electronics, fashion, and home essentials',
    image: '/images/banners/summer-sale.jpg',
    buttonText: 'Shop Now',
    buttonLink: '/products',
    backgroundColor: 'bg-gradient-to-r from-blue-600 to-purple-600',
  },
  {
    id: 2,
    title: 'New Arrivals',
    subtitle: 'Fresh Collection',
    description: 'Explore the latest trends in fashion and lifestyle products',
    image: '/images/banners/new-arrivals.jpg',
    buttonText: 'Explore',
    buttonLink: '/categories',
    backgroundColor: 'bg-gradient-to-r from-green-500 to-teal-600',
  },
  {
    id: 3,
    title: 'Electronics Mega Sale',
    subtitle: 'Best Prices Guaranteed',
    description: 'Latest smartphones, laptops, and gadgets at unbeatable prices',
    image: '/images/banners/electronics-sale.jpg',
    buttonText: 'Shop Electronics',
    buttonLink: '/categories/electronics',
    backgroundColor: 'bg-gradient-to-r from-orange-500 to-red-600',
  },
  {
    id: 4,
    title: 'Home & Garden',
    subtitle: 'Transform Your Space',
    description: 'Beautiful furniture and decor to make your house a home',
    image: '/images/banners/home-garden.jpg',
    buttonText: 'Shop Home',
    buttonLink: '/categories/home-garden',
    backgroundColor: 'bg-gradient-to-r from-emerald-500 to-cyan-600',
  },
  {
    id: 5,
    title: 'Fashion Forward',
    subtitle: 'Style That Speaks',
    description: 'Trendy clothing and accessories for every occasion',
    image: '/images/banners/fashion.jpg',
    buttonText: 'Shop Fashion',
    buttonLink: '/categories/fashion',
    backgroundColor: 'bg-gradient-to-r from-pink-500 to-rose-600',
  },
];

export default function HeroBanner() {
  const [slides, setSlides] = useState<BannerSlide[]>(defaultSlides);
  const [isLoading, setIsLoading] = useState(false); // Start with false to show default slides immediately
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    console.log('HeroBanner component mounted/re-mounted');
    setMounted(true);

    // Ensure we always have default slides available
    if (slides.length === 0) {
      console.log('No slides available, setting default slides');
      setSlides(defaultSlides);
    }

    // Load banner slides from Strapi CMS using frontend API routes
    const loadSlidesFromStrapi = async () => {
      try {
        console.log('🚀 Loading banner slides from frontend API...');

        // Use frontend API route instead of direct Strapi call
        const response = await fetch('/api/banners');

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        console.log('✅ Banner data received from frontend API:', result);

        // Transform frontend API data to match our BannerSlide interface
        if (result.success && result.data && Array.isArray(result.data) && result.data.length > 0) {
          const transformedSlides: BannerSlide[] = result.data
            .filter((item: any) => {
              // Handle both direct Strapi format and API service format
              const isActive = item.attributes?.isActive ?? item.active ?? true;
              return isActive;
            })
            .sort((a: any, b: any) => {
              const positionA = a.attributes?.position ?? a.position ?? 0;
              const positionB = b.attributes?.position ?? b.position ?? 0;
              return positionA - positionB;
            })
            .map((item: any) => {
              // Handle both Strapi v4 format (with attributes) and direct format
              const attributes = item.attributes || item;
              const imageUrl = attributes.image?.data?.attributes?.url || attributes.image?.url;

              return {
                id: item.id,
                title: attributes.title || 'Welcome to ONDC Seller',
                subtitle: attributes.subtitle || 'Discover Amazing Deals',
                description:
                  attributes.description || 'Explore our wide range of products and services',
                image: imageUrl
                  ? imageUrl.startsWith('http')
                    ? imageUrl
                    : `http://localhost:1339${imageUrl}`
                  : '/images/banners/default-banner.jpg',
                buttonText: attributes.buttonText || 'Shop Now',
                buttonLink: attributes.buttonLink || attributes.link || '/products',
                backgroundColor:
                  attributes.backgroundColor || 'bg-gradient-to-r from-blue-600 to-purple-600',
              };
            });

          console.log('✅ Transformed slides:', transformedSlides);
          setSlides(transformedSlides);
        } else {
          console.log('⚠️ No banner data found in Strapi, keeping default slides');
          setSlides(defaultSlides);
        }
      } catch (error) {
        console.error('❌ Error loading banner slides from Strapi:', error);
        console.log('🔄 Using default slides due to error');
        setSlides(defaultSlides);
      }
    };

    // Load Strapi data in background without blocking the UI
    loadSlidesFromStrapi();
  }, []); // Keep empty dependency array but add mounted state for debugging

  // Add cleanup effect
  useEffect(() => {
    return () => {
      console.log('HeroBanner component unmounting');
    };
  }, []);

  // Safety check - if no slides available, show default slides
  const slidesToShow = slides.length > 0 ? slides : defaultSlides;

  if (isLoading) {
    return (
      <div className="relative h-96 md:h-[500px] lg:h-[600px] bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="absolute inset-0 bg-black/20" />
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 h-full flex items-center">
          <div className="max-w-2xl">
            <div className="h-6 bg-white/20 rounded mb-4 animate-pulse w-32"></div>
            <div className="h-12 bg-white/30 rounded mb-4 animate-pulse w-96"></div>
            <div className="h-6 bg-white/20 rounded mb-8 animate-pulse w-80"></div>
            <div className="h-12 bg-white/40 rounded animate-pulse w-32"></div>
          </div>
        </div>
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
          <div className="text-white/70 text-sm">Loading banner...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative h-96 md:h-[500px] lg:h-[600px] overflow-hidden">
      <Swiper
        modules={[Navigation, Pagination, Autoplay]}
        spaceBetween={0}
        slidesPerView={1}
        navigation={{
          prevEl: '.hero-prev',
          nextEl: '.hero-next',
        }}
        pagination={{
          clickable: true,
          bulletClass: 'swiper-pagination-bullet hero-bullet',
          bulletActiveClass: 'swiper-pagination-bullet-active hero-bullet-active',
        }}
        autoplay={{
          delay: 3500, // 3.5 seconds - within the 3-5 second requirement
          disableOnInteraction: false,
        }}
        loop={true}
        className="h-full"
      >
        {slidesToShow.map(slide => (
          <SwiperSlide key={slide.id}>
            <div className="relative h-full flex items-center">
              {/* Background Image */}
              <div
                className="absolute inset-0 bg-cover bg-center bg-no-repeat"
                style={{ backgroundImage: `url(${slide.image})` }}
              />

              {/* Overlay for better text readability */}
              <div className="absolute inset-0 bg-black/40" />

              {/* Content */}
              <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
                <div className="max-w-2xl">
                  <h2 className="text-sm font-semibold text-white/90 uppercase tracking-wide mb-2">
                    {slide.subtitle}
                  </h2>
                  <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-4">
                    {slide.title}
                  </h1>
                  <p className="text-lg md:text-xl text-white/90 mb-8 max-w-lg">
                    {slide.description}
                  </p>
                  <a
                    href={slide.buttonLink}
                    className="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-gray-900 bg-white hover:bg-gray-50 transition-colors duration-200 shadow-lg hover:shadow-xl"
                  >
                    {slide.buttonText}
                  </a>
                </div>
              </div>
            </div>
          </SwiperSlide>
        ))}
      </Swiper>

      {/* Custom Navigation Arrows */}
      <button className="hero-prev absolute left-4 top-1/2 -translate-y-1/2 z-20 p-2 rounded-full bg-white/20 hover:bg-white/30 text-white transition-all duration-200 backdrop-blur-sm">
        <ChevronLeftIcon className="h-6 w-6" />
      </button>
      <button className="hero-next absolute right-4 top-1/2 -translate-y-1/2 z-20 p-2 rounded-full bg-white/20 hover:bg-white/30 text-white transition-all duration-200 backdrop-blur-sm">
        <ChevronRightIcon className="h-6 w-6" />
      </button>

      {/* Custom Pagination Styles */}
      <style jsx global>{`
        .hero-bullet {
          width: 12px !important;
          height: 12px !important;
          background: rgba(255, 255, 255, 0.5) !important;
          opacity: 1 !important;
          margin: 0 6px !important;
          transition: all 0.3s ease !important;
        }
        .hero-bullet-active {
          background: white !important;
          transform: scale(1.2) !important;
        }
        .swiper-pagination {
          bottom: 20px !important;
        }
      `}</style>
    </div>
  );
}
