/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable React strict mode to catch potential issues
  reactStrictMode: true,

  // Enable SWC minification for better performance
  swcMinify: true,

  // Specify the source directory
  pageExtensions: ['ts', 'tsx', 'js', 'jsx'],

  // Disable ESLint during builds for now
  eslint: {
    ignoreDuringBuilds: true,
  },

  // Experimental features to help with hydration
  experimental: {
    // Optimize server-side React rendering to reduce hydration mismatches
    optimizeServerReact: true,
  },

  // Disable styled-jsx to fix React context issues
  compiler: {
    styledJsx: false,
  },

  // Note: suppressHydrationWarning is a React component prop, not a Next.js config option
  // We'll handle it at the component level instead

  // Enable font optimization for better performance
  optimizeFonts: true,
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'cdn.selldone.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'plus.unsplash.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'example.com',
      },

      {
        protocol: 'http',
        hostname: 'localhost',
        port: '1339',
        pathname: '/**',
      },
      // Add more domains for images
      {
        protocol: 'https',
        hostname: 'picsum.photos',
      },
      {
        protocol: 'https',
        hostname: 'placehold.co',
      },
      {
        protocol: 'https',
        hostname: 'via.placeholder.com',
      },
      {
        protocol: 'https',
        hostname: 'placeholder.com',
      },
    ],
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },
  // App Router is the default in Next.js 14
  env: {
    // Medusa backend connection
    NEXT_PUBLIC_MEDUSA_URL: process.env.NEXT_PUBLIC_MEDUSA_URL || 'http://localhost:9000',
    NEXT_PUBLIC_MEDUSA_API_URL: process.env.NEXT_PUBLIC_MEDUSA_API_URL || 'http://localhost:9001',

    // Strapi CMS connection
    NEXT_PUBLIC_STRAPI_API_URL: process.env.NEXT_PUBLIC_STRAPI_API_URL || 'http://localhost:1339',
    STRAPI_API_TOKEN:
      process.env.STRAPI_API_TOKEN ||
      'baf318e16d6f90dd6aea345fb8ea61b46caf027e63804a278a595a556ed9d71ba2a3e621c6b230fa66b9b6932ba36e65943b22128db2f61804920248671cb49918a8c7c55ded9c2009099f23199d5d67d135b99f34a45712ecfd3a728e162039dcbc1f745adee909af70e2cbcb7a7240f822958f365bfc6193754a08252be5d2',

    // API URL for the products page
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:9001',

    // ONDC seller app API connection
    NEXT_PUBLIC_ONDC_API_URL: process.env.NEXT_PUBLIC_ONDC_API_URL || 'http://localhost:5000',

    // ONDC credentials for authentication
    ONDC_PARTICIPANT_ID: process.env.ONDC_PARTICIPANT_ID,
    ONDC_SUBSCRIBER_ID: process.env.ONDC_SUBSCRIBER_ID,
    ONDC_SUBSCRIBER_URL: process.env.ONDC_SUBSCRIBER_URL,
    ONDC_REGISTRY_URL: process.env.ONDC_REGISTRY_URL,
    ONDC_AUTH_SIGNING_KEY: process.env.ONDC_AUTH_SIGNING_KEY,
    ONDC_ENCRYPTION_PUBLIC_KEY: process.env.ONDC_ENCRYPTION_PUBLIC_KEY,
    ONDC_ENCRYPTION_PRIVATE_KEY: process.env.ONDC_ENCRYPTION_PRIVATE_KEY,
  },
};

module.exports = nextConfig;
