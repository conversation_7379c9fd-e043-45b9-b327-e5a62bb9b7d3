{"kind": "collectionType", "collectionName": "product_categories", "info": {"singularName": "product-category", "pluralName": "product-categories", "displayName": "product_category", "description": ""}, "options": {"draftAndPublish": true}, "attributes": {"name": {"type": "string"}, "description": {"type": "blocks"}, "image": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images"]}, "slug": {"type": "uid", "targetField": "name"}, "isSubcategory": {"type": "boolean", "default": false}, "parent": {"type": "relation", "relation": "manyToOne", "target": "api::product-category.product-category", "inversedBy": "children"}, "children": {"type": "relation", "relation": "oneToMany", "target": "api::product-category.product-category", "mappedBy": "parent"}, "products": {"type": "relation", "relation": "manyToMany", "target": "api::product.product", "mappedBy": "categories"}}}