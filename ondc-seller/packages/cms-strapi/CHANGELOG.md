# Strapi CMS Change Log

This file documents all notable changes to the Strapi CMS implementation in the ONDC Seller Platform.

## [Unreleased]

## [0.2.0] - 2025-06-12

### Changed

- **Product Category Schema Update**: Renamed `featured` field to `isSubcategory` in product-category collection type
  - Updated field type to boolean with default value `false`
  - Improved semantic meaning for category-subcategory hierarchy system
  - Maintains backward compatibility through frontend transformation functions
  - Aligns with existing parent/children relationship structure

### Technical Details

- Modified: `src/api/product-category/content-types/product-category/schema.json`
- Field purpose: Identify subcategories in hierarchy system
- Frontend support: Already implemented in `lib/strapi-api.ts`
- API compatibility: Maintained through transform functions

## [0.1.0] - 2025-05-19

### Added

- Initial Strapi CMS setup with PostgreSQL database
- Created all content types as specified in content-types.md:
  - Seller
  - Product Category
  - Product
  - Order
  - Order Item
  - Customer
  - Banner
  - Page
- Created components:
  - Address
  - SEO
  - Dimensions
  - Attribute
- Added relations between content types:
  - Product to Seller (manyToOne)
  - Product to Product Category (manyToMany)
  - Order to Customer (manyToOne)
  - Order to Order Item (oneToMany)
  - Product Category to Product Category (parent/children hierarchy)
- Created sample-data-guide.md with instructions for creating sample entries
- Created api-endpoints.md documenting all available API endpoints
- Added TypeScript type definitions for all content types and components

### Fixed

- Corrected field types in Seller content type (phone, pincode)
- Fixed media field configurations to restrict to appropriate file types
- Updated relation definitions to ensure proper bidirectional relations
- Resolved issues with TypeScript type definitions

## [0.0.1] - 2025-05-18

### Added

- Initial project setup
- Basic Strapi configuration
- PostgreSQL database connection setup
