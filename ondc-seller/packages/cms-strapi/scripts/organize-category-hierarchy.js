/**
 * Category Hierarchy Organization Script
 *
 * This script organizes existing categories into a proper parent-child hierarchy
 * and updates the isSubcategory field accordingly.
 */

const axios = require('axios');

// Strapi API configuration
const STRAPI_URL = 'http://localhost:1339';
const API_TOKEN = process.env.STRAPI_API_TOKEN || '';

// Create axios instance for Strapi API
const strapiAPI = axios.create({
  baseURL: `${STRAPI_URL}/api`,
  headers: {
    'Content-Type': 'application/json',
    ...(API_TOKEN && { Authorization: `Bearer ${API_TOKEN}` }),
  },
});

// Define the category hierarchy structure
const CATEGORY_HIERARCHY = {
  // Electronics and subcategories
  Electronics: {
    subcategories: [
      'Smartphones',
      'Laptops & Computers',
      'Audio & Headphones',
      'Gaming',
      'Cameras & Photography',
    ],
  },

  // Fashion and subcategories
  'Fashion & Apparel': {
    subcategories: [
      "Men's Clothing",
      "Women's Clothing",
      'Shoes & Footwear',
      'Accessories',
      'Kids & Baby',
    ],
  },

  // Home & Garden and subcategories
  'Home & Garden': {
    subcategories: [
      'Furniture',
      'Home Decor',
      'Kitchen & Dining',
      'Garden & Outdoor',
      'Storage & Organization',
    ],
  },

  // Health & Beauty and subcategories
  'Health & Beauty': {
    subcategories: [
      'Skincare & Personal Care',
      'Makeup & Cosmetics',
      'Hair Care',
      'Health Supplements',
      'Wellness & Fitness',
    ],
  },

  // Sports & Outdoors and subcategories
  'Sports & Outdoors': {
    subcategories: [
      'Fitness Equipment',
      'Team Sports',
      'Outdoor Recreation',
      'Winter Sports',
      'Water Sports',
    ],
  },

  // Books & Media and subcategories
  'Books & Media': {
    subcategories: ['Books', 'Movies & TV', 'Music', 'Games & Toys', 'Educational'],
  },

  // Automotive and subcategories
  Automotive: {
    subcategories: ['Car Accessories', 'Car Parts', 'Motorcycle', 'Tools & Equipment'],
  },

  // Food & Beverages and subcategories
  'Food & Beverages': {
    subcategories: [
      'Fresh Produce',
      'Pantry Staples',
      'Beverages',
      'Snacks & Confectionery',
      'Gourmet & Specialty',
    ],
  },
};

/**
 * Get all existing categories from Strapi
 */
async function getAllCategories() {
  try {
    console.log('📂 Fetching all categories from Strapi...');
    const response = await strapiAPI.get('/product-categories?pagination[pageSize]=100');
    const categories = response.data.data || [];
    console.log(`✅ Found ${categories.length} categories`);
    return categories;
  } catch (error) {
    console.error('❌ Error fetching categories:', error.message);
    throw error;
  }
}

/**
 * Find category by name (case-insensitive, flexible matching)
 */
function findCategoryByName(categories, targetName) {
  return categories.find(cat => {
    const catName = cat.name.toLowerCase().trim();
    const target = targetName.toLowerCase().trim();

    // Exact match
    if (catName === target) return true;

    // Partial match for similar names
    if (catName.includes(target) || target.includes(catName)) return true;

    // Handle common variations
    const variations = {
      'fashion & apparel': ['fashion', 'fashion & apparel'],
      'home & garden': ['home & garden', 'home garden'],
      'health & beauty': ['health & beauty', 'beauty & health', 'beauty health'],
      'sports & outdoors': ['sports & outdoors', 'sports fitness', 'sports & fitness'],
      'books & media': ['books & media', 'books media'],
      'food & beverages': ['food & beverages', 'food beverages'],
    };

    for (const [key, values] of Object.entries(variations)) {
      if (values.includes(target) && values.includes(catName)) return true;
    }

    return false;
  });
}

/**
 * Update category with parent relationship and isSubcategory flag
 */
async function updateCategory(category, updateData) {
  try {
    // Use documentId for Strapi v5
    const categoryId = category.documentId || category.id;
    const response = await strapiAPI.put(`/product-categories/${categoryId}`, {
      data: updateData,
    });
    return response.data;
  } catch (error) {
    console.error(`❌ Error updating category ${categoryId}:`, error.message);
    console.error('Update data:', updateData);
    throw error;
  }
}

/**
 * Main function to organize category hierarchy
 */
async function organizeCategoryHierarchy() {
  console.log('🚀 Starting category hierarchy organization...');

  try {
    // Get all existing categories
    const categories = await getAllCategories();

    if (categories.length === 0) {
      console.log('⚠️ No categories found. Please populate categories first.');
      return;
    }

    // Track updates
    const updates = {
      parentCategories: [],
      subcategories: [],
      notFound: [],
    };

    // Process each parent category and its subcategories
    for (const [parentName, config] of Object.entries(CATEGORY_HIERARCHY)) {
      console.log(`\n📁 Processing parent category: ${parentName}`);

      // Find parent category
      const parentCategory = findCategoryByName(categories, parentName);

      if (!parentCategory) {
        console.log(`⚠️ Parent category "${parentName}" not found`);
        updates.notFound.push(parentName);
        continue;
      }

      // Update parent category
      console.log(`✅ Found parent: ${parentCategory.name} (ID: ${parentCategory.id})`);

      try {
        await updateCategory(parentCategory, {
          isSubcategory: false,
          parent: null,
        });
        updates.parentCategories.push(parentCategory.name);
        console.log(`✅ Updated parent category: ${parentCategory.name}`);
      } catch (error) {
        console.error(`❌ Failed to update parent category ${parentCategory.name}:`, error.message);
      }

      // Process subcategories
      for (const subcategoryName of config.subcategories) {
        const subcategory = findCategoryByName(categories, subcategoryName);

        if (!subcategory) {
          console.log(`⚠️ Subcategory "${subcategoryName}" not found`);
          updates.notFound.push(subcategoryName);
          continue;
        }

        console.log(`  ├── Found subcategory: ${subcategory.name} (ID: ${subcategory.id})`);

        try {
          await updateCategory(subcategory, {
            isSubcategory: true,
            parent: parentCategory.documentId || parentCategory.id,
          });
          updates.subcategories.push(subcategory.name);
          console.log(`  ✅ Updated subcategory: ${subcategory.name} → ${parentCategory.name}`);
        } catch (error) {
          console.error(`  ❌ Failed to update subcategory ${subcategory.name}:`, error.message);
        }
      }
    }

    // Summary report
    console.log('\n📊 HIERARCHY ORGANIZATION SUMMARY');
    console.log('=====================================');
    console.log(`✅ Parent categories updated: ${updates.parentCategories.length}`);
    console.log(`✅ Subcategories updated: ${updates.subcategories.length}`);
    console.log(`⚠️ Categories not found: ${updates.notFound.length}`);

    if (updates.notFound.length > 0) {
      console.log('\n⚠️ Categories not found:');
      updates.notFound.forEach(name => console.log(`  - ${name}`));
    }

    console.log('\n🎉 Category hierarchy organization completed!');
  } catch (error) {
    console.error('❌ Error organizing category hierarchy:', error.message);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  organizeCategoryHierarchy()
    .then(() => {
      console.log('✅ Script completed successfully');
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ Script failed:', error.message);
      process.exit(1);
    });
}

module.exports = {
  organizeCategoryHierarchy,
  CATEGORY_HIERARCHY,
};
